/**
 * Test utilities for development and debugging
 * These functions should only be used in development mode
 */

import axios from 'axios';
import { API_BASE_URL } from '../context/AuthContext';

/**
 * Create a test token for password reset testing
 * @param email - Email to associate with the test token
 * @returns Promise with token data
 */
export const createTestToken = async (email: string = '<EMAIL>') => {
  try {
    console.log('Creating test token for email:', email);
    
    const response = await axios.post(`${API_BASE_URL}/api/create-test-token`, {
      email
    });
    
    console.log('Test token created successfully:', response.data);
    return response.data;
    
  } catch (error) {
    console.error('Failed to create test token:', error);
    throw error;
  }
};

/**
 * Verify a token with the backend
 * @param token - Token to verify
 * @returns Promise with verification result
 */
export const verifyTestToken = async (token: string) => {
  try {
    console.log('Verifying token:', token);
    
    const response = await axios.get(`${API_BASE_URL}/api/verify-reset-token/${token}`);
    
    console.log('Token verification successful:', response.data);
    return response.data;
    
  } catch (error) {
    console.error('Token verification failed:', error);
    throw error;
  }
};

/**
 * Test the complete password reset flow
 * @param email - Email to test with
 * @returns Promise with test results
 */
export const testPasswordResetFlow = async (email: string = '<EMAIL>') => {
  try {
    console.log('🧪 Starting password reset flow test...');
    
    // Step 1: Create test token
    console.log('Step 1: Creating test token...');
    const tokenData = await createTestToken(email);
    
    // Step 2: Verify token
    console.log('Step 2: Verifying token...');
    const verificationData = await verifyTestToken(tokenData.token);
    
    // Step 3: Test password reset (without actually changing password)
    console.log('Step 3: Testing password reset endpoint...');
    // Note: We won't actually reset the password in the test
    
    console.log('✅ Password reset flow test completed successfully!');
    return {
      success: true,
      token: tokenData.token,
      email: verificationData.email,
      message: 'All tests passed'
    };
    
  } catch (error) {
    console.error('❌ Password reset flow test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Test failed'
    };
  }
};

/**
 * Check if backend is running and accessible
 * @returns Promise with backend status
 */
export const checkBackendStatus = async () => {
  try {
    console.log('Checking backend status...');
    
    // Try to hit a simple endpoint
    const response = await axios.get(`${API_BASE_URL}/api/verify-reset-token/test`);
    
    // We expect this to fail with 400 (invalid token), but it means backend is running
    return {
      running: true,
      message: 'Backend is running'
    };
    
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 400) {
        // 400 means backend is running but token is invalid (expected)
        return {
          running: true,
          message: 'Backend is running'
        };
      } else if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        return {
          running: false,
          message: 'Backend is not running or not accessible'
        };
      }
    }
    
    return {
      running: false,
      message: 'Backend status unknown',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Log current environment information
 */
export const logEnvironmentInfo = () => {
  console.log('🔧 Environment Information:');
  console.log('API Base URL:', API_BASE_URL);
  console.log('Platform:', process.env.EXPO_PLATFORM || 'unknown');
  console.log('Environment:', process.env.NODE_ENV || 'development');
  console.log('Timestamp:', new Date().toISOString());
};

/**
 * Test navigation with a real token
 * @param navigation - React Navigation object
 * @param email - Email for test token
 */
export const testNavigationWithRealToken = async (navigation: any, email: string = '<EMAIL>') => {
  try {
    console.log('🧪 Testing navigation with real token...');
    
    // Create test token
    const tokenData = await createTestToken(email);
    
    // Navigate to reset screen
    console.log('Navigating to ResetPassword screen with token:', tokenData.token);
    navigation.navigate('ResetPassword', { token: tokenData.token });
    
    return {
      success: true,
      token: tokenData.token,
      message: 'Navigation test successful'
    };
    
  } catch (error) {
    console.error('❌ Navigation test failed:', error);
    throw error;
  }
};

// Export all test functions
export const testUtils = {
  createTestToken,
  verifyTestToken,
  testPasswordResetFlow,
  checkBackendStatus,
  logEnvironmentInfo,
  testNavigationWithRealToken
};

export default testUtils;
