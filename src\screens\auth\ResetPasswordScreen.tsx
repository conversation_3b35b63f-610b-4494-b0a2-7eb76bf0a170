import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { TextInput, Button, Text, ActivityIndicator, IconButton } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { API_BASE_URL } from '../../context/AuthContext';
import { useRoute } from '@react-navigation/native';
import axios from 'axios';

interface ResetPasswordScreenProps {
  navigation: any;
}

export const ResetPasswordScreen = ({ navigation }: ResetPasswordScreenProps) => {
  const route = useRoute();
  const { token } = (route.params as { token?: string }) || {};
  const [formData, setFormData] = useState({
    newPassword: '',
    confirmPassword: '',
  });
  const [formError, setFormError] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(true);
  const [tokenValid, setTokenValid] = useState(false);
  const [userEmail, setUserEmail] = useState<string>('');
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  // Verify token on component mount
  useEffect(() => {
    const verifyToken = async () => {
      console.log('ResetPasswordScreen mounted');
      console.log('Route params:', route.params);
      console.log('Token from params:', token);

      if (!token) {
        console.log('No token found in route params');
        setFormError('Invalid reset link. Please request a new password reset.');
        setIsVerifying(false);
        return;
      }

      try {
        const response = await axios.get(`${API_BASE_URL}/api/verify-reset-token/${token}`);
        setTokenValid(true);
        setUserEmail(response.data.email);
      } catch (error) {
        if (axios.isAxiosError(error)) {
          setFormError(error.response?.data?.error || 'Invalid or expired reset link');
        } else {
          setFormError('Failed to verify reset link');
        }
      } finally {
        setIsVerifying(false);
      }
    };

    verifyToken();
  }, [token]);

  const validatePassword = (password: string): string | null => {
    if (!password) {
      return 'Password is required';
    }
    if (password.length < 6) {
      return 'Password must be at least 6 characters long';
    }
    if (password.length > 128) {
      return 'Password is too long';
    }
    return null;
  };

  const handleResetPassword = async () => {
    try {
      setFormError('');

      // Validate passwords
      const passwordError = validatePassword(formData.newPassword);
      if (passwordError) {
        throw new Error(passwordError);
      }

      if (formData.newPassword !== formData.confirmPassword) {
        throw new Error('Passwords do not match');
      }

      setIsLoading(true);

      const response = await axios.post(`${API_BASE_URL}/api/reset-password`, {
        token,
        newPassword: formData.newPassword,
      });

      // Show success message
      Alert.alert(
        'Password Reset Successful',
        'Your password has been reset successfully. You can now log in with your new password.',
        [
          {
            text: 'Go to Login',
            onPress: () => navigation.navigate('Login')
          }
        ]
      );

    } catch (err) {
      if (axios.isAxiosError(err)) {
        setFormError(err.response?.data?.error || 'Failed to reset password');
      } else {
        setFormError(err instanceof Error ? err.message : 'Failed to reset password');
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isVerifying) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Verifying reset link...</Text>
      </View>
    );
  }

  if (!tokenValid) {
    return (
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.container}>
          <Text style={styles.title}>Invalid Reset Link</Text>
          <Text style={styles.errorMessage}>{formError}</Text>

          <Button
            mode="contained"
            onPress={() => navigation.navigate('Login')}
            style={styles.button}
            buttonColor={theme.colors.primary}
          >
            Back to Login
          </Button>

          <Button
            mode="text"
            onPress={() => navigation.navigate('Login')}
            style={styles.linkButton}
            textColor={theme.colors.primary}
          >
            Request New Reset Link
          </Button>
        </View>
      </ScrollView>
    );
  }

  return (
    <ScrollView
      contentContainerStyle={styles.scrollContainer}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.container}>
        <Text style={styles.title}>Reset Your Password</Text>
        <Text style={styles.subtitle}>
          Enter a new password for {userEmail}
        </Text>

        {formError ? (
          <Text style={styles.error}>{formError}</Text>
        ) : null}

        <TextInput
          label="New Password"
          value={formData.newPassword}
          onChangeText={(text) => setFormData({ ...formData, newPassword: text })}
          style={styles.input}
          mode="outlined"
          secureTextEntry={!passwordVisible}
          activeOutlineColor={theme.colors.primary}
          left={<TextInput.Icon icon="lock" color={theme.colors.primary} />}
          right={
            <TextInput.Icon
              icon={passwordVisible ? "eye" : "eye-off"}
              color={theme.colors.primary}
              onPress={() => setPasswordVisible(!passwordVisible)}
            />
          }
          accessible={true}
          accessibilityLabel="Enter your new password"
          accessibilityRole="text"
        />

        <TextInput
          label="Confirm New Password"
          value={formData.confirmPassword}
          onChangeText={(text) => setFormData({ ...formData, confirmPassword: text })}
          style={styles.input}
          mode="outlined"
          secureTextEntry={!confirmPasswordVisible}
          activeOutlineColor={theme.colors.primary}
          left={<TextInput.Icon icon="lock-check" color={theme.colors.primary} />}
          right={
            <TextInput.Icon
              icon={confirmPasswordVisible ? "eye" : "eye-off"}
              color={theme.colors.primary}
              onPress={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
            />
          }
          accessible={true}
          accessibilityLabel="Confirm your new password"
          accessibilityRole="text"
        />

        <Button
          mode="contained"
          onPress={handleResetPassword}
          style={styles.button}
          buttonColor={theme.colors.primary}
          loading={isLoading}
          disabled={isLoading || !formData.newPassword || !formData.confirmPassword}
          accessible={true}
          accessibilityLabel="Reset password"
          accessibilityRole="button"
        >
          {isLoading ? 'Resetting...' : 'Reset Password'}
        </Button>

        <Button
          mode="text"
          onPress={() => navigation.navigate('Login')}
          style={styles.linkButton}
          textColor={theme.colors.primary}
          accessible={true}
          accessibilityLabel="Cancel and go back to login"
          accessibilityRole="button"
        >
          Back to Login
        </Button>

        <Text style={styles.note}>
          Password must be at least 6 characters long.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
  },
  container: {
    ...globalStyles.container,
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: 16,
    color: theme.colors.onSurface,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    color: theme.colors.primary,
    marginBottom: theme.spacing.md,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: theme.colors.onSurface,
    marginBottom: theme.spacing.lg,
  },
  input: {
    ...globalStyles.input,
  },
  button: {
    ...globalStyles.button,
  },
  linkButton: {
    marginTop: theme.spacing.sm,
  },
  error: {
    ...globalStyles.error,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  errorMessage: {
    fontSize: 16,
    color: theme.colors.error,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    lineHeight: 22,
  },
  note: {
    fontSize: 14,
    color: theme.colors.placeholder,
    textAlign: 'center',
    marginTop: theme.spacing.md,
    fontStyle: 'italic',
  },
});
