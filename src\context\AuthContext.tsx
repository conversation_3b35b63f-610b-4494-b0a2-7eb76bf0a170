import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import type { User, LoginData, RegisterData, CreateStaffData, Patient } from '../types';
import { transformPatient } from '../utils/dataTransformers';

export const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://127.0.0.1:5000';

interface AuthContextType {
  fetchPatients: () => Promise<Patient[]>;
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
  login: (data: LoginData) => Promise<User>;
  register: (data: RegisterData) => Promise<User>;
  createStaffMember: (data: CreateStaffData) => Promise<void>;
  logout: () => Promise<void>;
  updateRegistrationdata: (updatedUser: User) => Promise<void>;
  deleteStaffMember: (staffId: string) => Promise<void>;
  currentUser: User | null;
  staffList: User[];
  fetchStaffData: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [staffList, setStaffList] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadUserAndToken = async () => {
      try {
        const [storedUser, storedToken] = await Promise.all([
          AsyncStorage.getItem('user'),
          AsyncStorage.getItem('token')
        ]);

        if (storedUser) {
          setUser(JSON.parse(storedUser));
        }

        if (storedToken) {
          setToken(storedToken);
        }
      } catch (err) {
        console.error('Error loading stored user or token:', err);
      } finally {
        setIsLoading(false);
      }
    };
    loadUserAndToken();
  }, []);

  const login = useCallback(async (data: LoginData): Promise<User> => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await axios.post(`${API_BASE_URL}/api/login`, data);
      // Transform the user data to ensure consistent id property
      const transformedUser = transformPatient(response.data.user);

      if (!transformedUser) {
        throw new Error('Failed to transform user data');
      }

      // Convert the transformed user to User type
      const loggedInUser: User = {
        ...transformedUser,
        // Add any required fields that might be missing
        sex: transformedUser.sex || '',
        pregnant: transformedUser.pregnant || '',
        priorCounsel: transformedUser.priorCounsel || '',
        dateOfBirth: transformedUser.dateOfBirth || '',
        hospitalNo: transformedUser.hospitalNo || '',
        firstName: transformedUser.firstName || '',
        lastName: transformedUser.lastName || '',
        age: transformedUser.age || 0,
        weight: transformedUser.weight || null,
        contactNumber: transformedUser.contactNumber || '',
        ethnicity: transformedUser.ethnicity || '',
        email: transformedUser.email || '',
        name: transformedUser.name || '',
        role: transformedUser.role || 'patient',
        phoneNumber: transformedUser.phoneNumber || '',
        designation: transformedUser.designation || '',
        department: transformedUser.department || '',
      };

      const token = response.data.token;
      await AsyncStorage.setItem('user', JSON.stringify(loggedInUser));
      await AsyncStorage.setItem('token', token);
      setUser(loggedInUser);
      setToken(token);
      return loggedInUser;
    } catch (err) {
      if (axios.isAxiosError(err)) {
        setError(err.response?.data?.error || 'An error occurred during login');
      } else {
        setError('An unexpected error occurred.');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const register = useCallback(async (data: RegisterData): Promise<User> => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await axios.post(`${API_BASE_URL}/api/register`, data);
      // Transform the user data to ensure consistent id property
      const transformedUser = transformPatient(response.data.user);

      if (!transformedUser) {
        throw new Error('Failed to transform user data');
      }

      // Convert the transformed user to User type
      const registeredUser: User = {
        ...transformedUser,
        // Add any required fields that might be missing
        sex: transformedUser.sex || '',
        pregnant: transformedUser.pregnant || '',
        priorCounsel: transformedUser.priorCounsel || '',
        dateOfBirth: transformedUser.dateOfBirth || '',
        hospitalNo: transformedUser.hospitalNo || '',
        firstName: transformedUser.firstName || '',
        lastName: transformedUser.lastName || '',
        age: transformedUser.age || 0,
        weight: transformedUser.weight || null,
        contactNumber: transformedUser.contactNumber || '',
        ethnicity: transformedUser.ethnicity || '',
        email: transformedUser.email || '',
        name: transformedUser.name || '',
        role: transformedUser.role || 'patient',
        phoneNumber: transformedUser.phoneNumber || '',
        designation: transformedUser.designation || '',
        department: transformedUser.department || '',
      };

      await AsyncStorage.setItem('user', JSON.stringify(registeredUser));
      setUser(registeredUser);
      return registeredUser;
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const errorMessage = err.response?.data?.error || 'An error occurred during registration';
        setError(errorMessage);
        throw err;
      } else {
        const errorMessage = 'An unexpected error occurred.';
        setError(errorMessage);
        throw new Error(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchPatients = useCallback(async (): Promise<Patient[]> => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/patients`);

      // First, ensure we have the id property consistently
      const transformedData = response.data.map((patient: any) => {
        // If the patient has _id, use it as id
        if (patient._id) {
          const { _id, ...rest } = patient;
          return { ...rest, id: _id };
        }
        // If the patient already has id, return as is
        return patient;
      });

      // Convert to Patient type with required fields
      const patients: Patient[] = transformedData.map((patient: any) => ({
        id: patient.id,
        hospitalNo: patient.hospitalNo || '',
        name: patient.name || '',
        firstName: patient.firstName || '',
        lastName: patient.lastName || '',
        email: patient.email || '',
        age: patient.age || 0,
        sex: patient.sex || '',
        contactNumber: patient.contactNumber || '',
        address: patient.address || '',
        medicalHistory: patient.medicalHistory || [],
        assignedDoctor: patient.assignedDoctor || '',
        status: patient.status as 'active' | 'inactive' || 'active',
        createdAt: patient.createdAt || new Date().toISOString()
      }));

      return patients;
    } catch (err) {
      console.error('Error fetching patients:', err);
      throw err;
    }
  }, []);

  const fetchStaffData = useCallback(async (): Promise<void> => {
    try {
      const token = await AsyncStorage.getItem('token');
      if (!token) throw new Error('No authentication token found');

      const response = await axios.get(`${API_BASE_URL}/api/staff`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      // First, ensure we have the id property consistently
      const transformedData = response.data.map((staff: any) => {
        // If the staff has _id, use it as id
        if (staff._id) {
          const { _id, ...rest } = staff;
          return { ...rest, id: _id };
        }
        // If the staff already has id, return as is
        return staff;
      });

      // Convert to User type with required fields
      const staffList: User[] = transformedData.map((staff: any) => ({
        id: staff.id,
        sex: staff.sex || '',
        pregnant: staff.pregnant || '',
        priorCounsel: staff.priorCounsel || '',
        dateOfBirth: staff.dateOfBirth || '',
        hospitalNo: staff.hospitalNo || '',
        firstName: staff.firstName || '',
        lastName: staff.lastName || '',
        age: staff.age || 0,
        weight: staff.weight || null,
        contactNumber: staff.contactNumber || '',
        ethnicity: staff.ethnicity || '',
        email: staff.email || '',
        name: staff.name || '',
        username: staff.username,
        role: staff.role || 'doctor',
        createdAt: staff.createdAt,
        status: staff.status || 'active',
        phoneNumber: staff.phoneNumber || '',
        designation: staff.designation || '',
        department: staff.department || '',
        specialization: staff.specialization
      }));

      console.log('Staff data fetched:', staffList);
      setStaffList(staffList);
    } catch (err) {
      console.error('Error fetching staff:', err);
      throw err;
    }
  }, []);

  const createStaffMember = useCallback(async (data: CreateStaffData): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);
      const token = await AsyncStorage.getItem('token');
      if (!token) throw new Error('No authentication token found');

      await axios.post(`${API_BASE_URL}/api/staff`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      await fetchStaffData(); // Refresh list after creation
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred creating staff member');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [fetchStaffData]);

  const deleteStaffMember = useCallback(async (staffId: string): Promise<void> => {
    try {
      console.log('Attempting to delete staff member with ID:', staffId); // Log before request
      setIsLoading(true);
      setError(null);
      const token = await AsyncStorage.getItem('token');
      console.log('Token fetched for delete:', token); // Log token
      if (!token) throw new Error('No authentication token found');

      const response = await axios.delete(`${API_BASE_URL}/api/staff/${staffId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      console.log('Delete response:', response); // Log response
      await fetchStaffData(); // Refresh list after deletion
    } catch (err) {
      console.error('Error deleting staff member:', err); // Log the error
      if (axios.isAxiosError(err)) {
        setError(err.response?.data?.error || 'An error occurred while deleting staff member');
      } else {
        setError('An unexpected error occurred.');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [fetchStaffData]);

  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await AsyncStorage.removeItem('user');
      await AsyncStorage.removeItem('token');
      setUser(null);
      setToken(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during logout');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateRegistrationdata = useCallback(async (updatedUser: User): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);
      const token = await AsyncStorage.getItem('token');
      if (!token) throw new Error('No authentication token found');

      // Extract id and create update data
      const { id, ...updateData } = updatedUser;

      const response = await axios.put(`${API_BASE_URL}/api/patients/update-profile/${id}`, updateData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      // Transform the response data to ensure consistent id property
      const responseData = response.data.user;

      // Ensure the user has the id property
      let transformedUser = responseData;
      if (responseData._id && !responseData.id) {
        const { _id, ...rest } = responseData;
        transformedUser = { ...rest, id: _id };
      }

      // Convert to User type
      const updatedUserData: User = {
        ...transformedUser,
        // Add any required fields that might be missing
        sex: transformedUser.sex || '',
        pregnant: transformedUser.pregnant || '',
        priorCounsel: transformedUser.priorCounsel || '',
        dateOfBirth: transformedUser.dateOfBirth || '',
        hospitalNo: transformedUser.hospitalNo || '',
        firstName: transformedUser.firstName || '',
        lastName: transformedUser.lastName || '',
        age: transformedUser.age || 0,
        weight: transformedUser.weight || null,
        contactNumber: transformedUser.contactNumber || '',
        ethnicity: transformedUser.ethnicity || '',
        email: transformedUser.email || '',
        name: transformedUser.name || '',
        role: transformedUser.role || 'patient',
        phoneNumber: transformedUser.phoneNumber || '',
        designation: transformedUser.designation || '',
        department: transformedUser.department || '',
      };

      await AsyncStorage.setItem('user', JSON.stringify(updatedUserData));
      setUser(updatedUserData);
    } catch (err) {
      if (axios.isAxiosError(err)) {
        setError(err.response?.data?.error || 'An error occurred while updating profile');
      } else {
        setError('An unexpected error occurred.');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user,
        token,
        isLoading,
        error,
        login,
        register,
        createStaffMember,
        fetchPatients,
        logout,
        updateRegistrationdata,
        deleteStaffMember,
        currentUser: user,
        staffList,
        fetchStaffData,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
