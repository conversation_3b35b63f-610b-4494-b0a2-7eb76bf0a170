import React, { useState, useCallback } from 'react';
import { View, StyleSheet, ScrollView, Image, Alert } from 'react-native';
import { TextInput, Button, Text, ActivityIndicator, IconButton, Portal, Modal } from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth } from '../../context/AuthContext';
import { validateEmailForPasswordReset } from '../../utils/emailValidation';
import { getButtonAccessibilityProps, getModalAccessibilityProps, getTextInputAccessibilityProps } from '../../utils/accessibilityUtils';

export const LoginScreen = ({ navigation }: any) => {
  const { login, isLoading, error, requestPasswordReset } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [formError, setFormError] = useState<string>('');
  const [passwordVisible, setPasswordVisible] = useState(false);

  // Forgot Password state
  const [showForgotPasswordModal, setShowForgotPasswordModal] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [resetEmailError, setResetEmailError] = useState('');
  const [isResetLoading, setIsResetLoading] = useState(false);
  const [resetRequestSent, setResetRequestSent] = useState(false);
  const [lastResetRequest, setLastResetRequest] = useState<number>(0);

  const handleLogin = async () => {
    try {
      setFormError('');

      if (!formData.email || !formData.password) {
        throw new Error('Please fill in all fields');
      }

      await login(formData);

    } catch (err) {
      setFormError(err instanceof Error ? err.message : 'Login failed');
    }
  };

  // Rate limiting: prevent multiple requests within 60 seconds
  const RATE_LIMIT_DURATION = 60000; // 60 seconds

  const handleForgotPassword = useCallback(() => {
    setResetEmail(formData.email); // Pre-fill with login email if available
    setResetEmailError('');
    setResetRequestSent(false);
    setShowForgotPasswordModal(true);
  }, [formData.email]);

  const handlePasswordResetRequest = useCallback(async () => {
    try {
      setResetEmailError('');

      // Check rate limiting
      const now = Date.now();
      if (lastResetRequest && (now - lastResetRequest) < RATE_LIMIT_DURATION) {
        const remainingTime = Math.ceil((RATE_LIMIT_DURATION - (now - lastResetRequest)) / 1000);
        throw new Error(`Please wait ${remainingTime} seconds before requesting another reset`);
      }

      // Validate email
      const validation = validateEmailForPasswordReset(resetEmail);
      if (!validation.isValid) {
        throw new Error(validation.error || 'Invalid email address');
      }

      setIsResetLoading(true);
      await requestPasswordReset(resetEmail);

      // Update rate limiting
      setLastResetRequest(now);
      setResetRequestSent(true);

      // Show success message
      Alert.alert(
        'Reset Email Sent',
        'If an account with this email exists, you will receive password reset instructions shortly. Please check your email and follow the instructions to reset your password.',
        [
          {
            text: 'OK',
            onPress: () => {
              setShowForgotPasswordModal(false);
              setResetEmail('');
              setResetRequestSent(false);
            }
          }
        ]
      );

    } catch (err) {
      setResetEmailError(err instanceof Error ? err.message : 'Failed to send reset email');
    } finally {
      setIsResetLoading(false);
    }
  }, [resetEmail, lastResetRequest, requestPasswordReset]);

  const handleCloseForgotPasswordModal = useCallback(() => {
    setShowForgotPasswordModal(false);
    setResetEmail('');
    setResetEmailError('');
    setResetRequestSent(false);
  }, []);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <ScrollView
      contentContainerStyle={styles.scrollContainer}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.container}>
        <Image source={require('../../../assets/favicon.png')} style={styles.logo} />
      <Text style={styles.title}>Adverse Drug Reaction Reporting System</Text>
      <Text style={styles.note}> </Text>
      <Text style={styles.note}><Text style={{ fontWeight: 'bold' }}>Confidentiality</Text>{'\n'}Any information related to the identities of the reporter and patient will be kept confidential.</Text>
      <Text style={styles.note}><Text style={{ fontWeight: 'bold' }}>What to Report</Text>{'\n'}Any reactions or effects which is noxious(harmful) and/or unintended, and which occurs at doses normally used for prophylaxis, diagnosis, or treatment of a disease, or for the modification of a physiological function.</Text>
      <Text style={styles.note}> </Text>

        {(formError || error) && (
          <Text style={styles.error}>{formError || error}</Text>
        )}

        <TextInput
          label="Email or Username"
          value={formData.email}
          onChangeText={(text) => setFormData({ ...formData, email: text })}
          style={styles.input}
          mode="outlined"
          autoCapitalize="none"
          activeOutlineColor={theme.colors.primary}
          left={<TextInput.Icon icon="account" color={theme.colors.primary} />}
        />

        <TextInput
          label="Password"
          value={formData.password}
          onChangeText={(text) => setFormData({ ...formData, password: text })}
          style={styles.input}
          mode="outlined"
          secureTextEntry={!passwordVisible}
          activeOutlineColor={theme.colors.primary}
          left={<TextInput.Icon icon="lock" color={theme.colors.primary} />}
          right={
            <TextInput.Icon
              icon={passwordVisible ? "eye" : "eye-off"}
              color={theme.colors.primary}
              onPress={() => setPasswordVisible(!passwordVisible)}
            />
          }
        />

        <Button
          mode="contained"
          onPress={handleLogin}
          style={styles.button}
          buttonColor={theme.colors.primary}
          accessible={true}
          accessibilityLabel="Login to your account"
          accessibilityRole="button"
        >
          Login
        </Button>

        <Button
          mode="text"
          onPress={handleForgotPassword}
          style={styles.linkButton}
          textColor={theme.colors.primary}
          accessible={true}
          accessibilityLabel="Reset your password"
          accessibilityRole="button"
        >
          Forgot Password?
        </Button>

        <Button
          mode="text"
          onPress={() => navigation.navigate('Register')}
          style={styles.linkButton}
          textColor={theme.colors.primary}
          accessible={true}
          accessibilityLabel="Create a new patient account"
          accessibilityRole="button"
        >
          New patient? Create account
        </Button>

      <Text style={styles.note}> </Text>
      <Text style={styles.note}>Pharmacovigilance Unit encourages the reporting of all suspected adverse reactions to drugs and other medicinal substances(including herbal, traditional or alternative remedies)</Text>
      <Text style={styles.note}>Please report even you are not certain the product caused the adverse reaction</Text>
      </View>

      {/* Forgot Password Modal */}
      <Portal>
        <Modal
          visible={showForgotPasswordModal}
          onDismiss={handleCloseForgotPasswordModal}
          contentContainerStyle={styles.modalContainer}
          dismissable={true}
          dismissableBackButton={true}
        >
          <Text style={styles.modalTitle}>Reset Password</Text>
          <Text style={styles.modalDescription}>
            Enter your email address and we'll send you instructions to reset your password.
          </Text>

          {resetEmailError ? (
            <Text style={styles.error}>{resetEmailError}</Text>
          ) : null}

          {resetRequestSent ? (
            <View style={styles.successContainer}>
              <Text style={styles.successText}>
                Reset instructions sent! Please check your email.
              </Text>
            </View>
          ) : (
            <>
              <TextInput
                label="Email Address"
                value={resetEmail}
                onChangeText={setResetEmail}
                style={styles.input}
                mode="outlined"
                autoCapitalize="none"
                keyboardType="email-address"
                activeOutlineColor={theme.colors.primary}
                left={<TextInput.Icon icon="email" color={theme.colors.primary} />}
                accessible={true}
                accessibilityLabel="Enter your email address for password reset"
                accessibilityRole="text"
              />

              <View style={styles.modalButtons}>
                <Button
                  mode="outlined"
                  onPress={handleCloseForgotPasswordModal}
                  style={styles.modalButton}
                  accessible={true}
                  accessibilityLabel="Cancel password reset"
                  accessibilityRole="button"
                >
                  Cancel
                </Button>
                <Button
                  mode="contained"
                  onPress={handlePasswordResetRequest}
                  style={styles.modalButton}
                  buttonColor={theme.colors.primary}
                  loading={isResetLoading}
                  disabled={isResetLoading || !resetEmail.trim()}
                  accessible={true}
                  accessibilityLabel="Send password reset email"
                  accessibilityRole="button"
                >
                  {isResetLoading ? 'Sending...' : 'Send Reset Email'}
                </Button>
              </View>
            </>
          )}
        </Modal>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
  },
  container: {
    ...globalStyles.container,
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    color: theme.colors.primary,
    marginBottom: theme.spacing.lg,
  },
  input: {
    ...globalStyles.input,
  },
  button: {
    ...globalStyles.button,
  },
  linkButton: {
    marginTop: theme.spacing.sm,
  },
  error: {
    ...globalStyles.error,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  note: {
    fontSize: 16,
    color: theme.colors.placeholder,
    textAlign: 'center',
    marginTop: theme.spacing.md,
    fontStyle: 'italic',
  },
  note_text: {
    fontSize: 16,
    color: theme.colors.placeholder,
    marginTop: theme.spacing.md,
    fontStyle: 'italic',
  },
  logo: {
    width: 300,
    height: 200,
    alignSelf: 'center',
    marginBottom: theme.spacing.lg,
  },
  // Forgot Password Modal Styles
  modalContainer: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    margin: theme.spacing.lg,
    borderRadius: theme.spacing.sm,
    maxWidth: 400,
    alignSelf: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  modalDescription: {
    fontSize: 16,
    color: theme.colors.onSurface,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    lineHeight: 22,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  modalButton: {
    flex: 1,
  },
  successContainer: {
    backgroundColor: theme.colors.primaryContainer,
    padding: theme.spacing.md,
    borderRadius: theme.spacing.sm,
    marginVertical: theme.spacing.md,
  },
  successText: {
    color: theme.colors.onPrimaryContainer,
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '500',
  },
});
