import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Image, TouchableOpacity, Dimensions } from 'react-native';
import {
  Text,
  Card,
  Button,
  Chip,
  Portal,
  Modal,
  ActivityIndicator,
  IconButton,
  Divider,
  SegmentedButtons,
  TextInput // Added import for TextInput
} from 'react-native-paper';
import axios from 'axios';
import { theme, styles as globalStyles } from '../../theme';
import { useDrugReaction } from '../../context/DrugReactionContext';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import type { DrugReaction, ReactionStatus, ReactionSeverity } from '../../types/drugReaction';

interface UpdateData {
  status: ReactionStatus;
  treatmentGiven: string;
  hospitalized: boolean;
  allergyCard: boolean;
  discontinuation: boolean;
  timeAfterExposure: string;
  resolvedBy?: string;
  resolvedAt?: string;
}

export const ReactionManagementScreen = () => {
  const { reactions: contextReactions, getReactions, updateReaction, isLoading } = useDrugReaction();
  const [reactions, setReactions] = useState<DrugReaction[]>([]);
  const [selectedReaction, setSelectedReaction] = useState<DrugReaction | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [updateNote, setUpdateNote] = useState('');
  const [hospitalized, setHospitalized] = useState<string>('');
  const [allergyCard, setallergyCard] = useState<string>('');
  const [discontinuation, setDiscontinuation] = useState<string>('');
  const [timeAfterExposure, setTimeAfterExposure] = useState('');
  const { user } = useAuth();

  useEffect(() => {
    loadReactions();
  }, []);

  const loadReactions = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/drug-reactions`);
      const fetchedReactions = response.data;

      // Separate pending and resolved reactions
      const pendingReactions = fetchedReactions.filter((reaction: DrugReaction) => reaction.status === 'pending');
      const resolvedReactions = fetchedReactions.filter((reaction: DrugReaction) => reaction.status === 'resolved');

      // Combine them with pending first and resolved at the bottom
      const sortedReactions = [...pendingReactions, ...resolvedReactions];

      setReactions(sortedReactions);  // Update the state with the sorted reactions
      console.log('Fetched reactions:', fetchedReactions);
      console.log('User data for each reaction:', fetchedReactions.map((reaction: DrugReaction) => reaction.user));
    } catch (error) {
      console.error('Failed to load reactions:', error);
    }
  };


  const handleUpdateStatus = async (newStatus: ReactionStatus) => {
    if (!selectedReaction || !user?.id) return;

    try {
      const updateData: UpdateData = {
        status: newStatus,
        treatmentGiven: updateNote,
        hospitalized: hospitalized === 'yes',
        allergyCard: allergyCard === 'yes',
        discontinuation: discontinuation === 'yes',
        timeAfterExposure: timeAfterExposure
      };

      // Add resolvedBy field only when marking as resolved
      if (newStatus === 'resolved') {
        updateData.resolvedBy = user.id;
        updateData.resolvedAt = new Date().toISOString();
      }

      const response = await axios.put(`${API_BASE_URL}/api/drug-reactions/${selectedReaction._id}`, updateData);

      if (response.status === 200) {
        loadReactions();
        setShowDetailsModal(false);
      } else {
        console.error('Failed to update reaction:', response.data);
      }
    } catch (error) {
      console.error('Failed to update reaction:', error);
    }
  };

  const getSeverityColor = (severity: ReactionSeverity) => {
    switch (severity) {
      case 'mild':
        return theme.colors.primary;
      case 'moderate':
        return '#FFA500';
      case 'severe':
        return theme.colors.error;
      default:
        return theme.colors.text;
    }
  };

  const getStatusColor = (status: ReactionStatus) => {
    switch (status) {
      case 'pending':
        return theme.colors.error;
      case 'resolved':
        return theme.colors.secondary;
      default:
        return theme.colors.text;
    }
  };

  const safeReactions = Array.isArray(contextReactions) ? contextReactions : [];

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Drug Reactions</Text>
      </View>

      <ScrollView style={styles.reactionsList}>
          {reactions.map((reaction: DrugReaction) => (
              <Card
                  key={reaction._id}
                  style={styles.reactionCard}
                  onPress={() => {
                      setSelectedReaction(reaction);
                      setShowDetailsModal(true);
                  }}
              >
                  <Card.Content>
                      <View style={styles.cardHeader}>
                          <View>
                              <Text style={styles.caseId}>
                                  {reaction.user ? `${reaction.user.firstName} ${reaction.user.lastName}` : 'Unknown Patient'}
                              </Text>
                              <Text style={styles.age}>Age: {reaction.user.age ?? 'N/A'}</Text>
                              <Text style={styles.sex}>Sex: {reaction.user.sex ?? 'N/A'}</Text>
                              <Text style={styles.contactNumber}>Contact Number: {reaction.user.contactNumber}</Text>
                              <Text style={styles.drugName}>{reaction.drugName}</Text>
                              <Text numberOfLines={2} style={styles.description}>
                          {reaction.reactionDetails}
                      </Text>
                          </View>
                          <Chip
                              mode="outlined"
                              textStyle={{ color: getStatusColor(reaction.status) }}
                          >
                              {reaction.status}
                          </Chip>
                      </View>

                      <View style={styles.severityContainer}>
                          <Chip
                              mode="flat"
                              style={{ backgroundColor: getSeverityColor(reaction.severity) + '20' }}
                              textStyle={{ color: getSeverityColor(reaction.severity) }}
                          >
                          {reaction.severity}
                          </Chip>
                          <Text style={styles.date}>
                              Reported: {new Date(reaction.dateReported).toLocaleDateString()}
                          </Text>
                      </View>
                  </Card.Content>
              </Card>
          ))}
      </ScrollView>

      <Portal>
        <Modal
          visible={showDetailsModal}
          onDismiss={() => setShowDetailsModal(false)}
          contentContainerStyle={styles.modalContainer}
        >
          {selectedReaction && (
            <>
              <ScrollView style={styles.modalScrollView}>
                <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Reaction Details</Text>
                <IconButton
                  icon="close"
                  size={24}
                  onPress={() => setShowDetailsModal(false)}
                  style={styles.modalCloseButton}
                />
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Case ID:</Text>
                <Text style={styles.detailValue}>{selectedReaction._id}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Drug:</Text>
                <Text style={styles.detailValue}>{selectedReaction.drugName}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Started:</Text>
                <Text style={styles.detailValue}>
                  {selectedReaction.dateStarted ? new Date(selectedReaction.dateStarted).toLocaleDateString() : 'Not specified'}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Stopped:</Text>
                <Text style={styles.detailValue}>
                  {selectedReaction.dateStopped ? new Date(selectedReaction.dateStopped).toLocaleDateString() : 'Not specified'}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Daily Dose:</Text>
                <Text style={styles.detailValue}>{selectedReaction.dailyDose || 'Not specified'}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Route:</Text>
                <Text style={styles.detailValue}>{selectedReaction.routeOfAdministration || 'Not specified'}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Dosage Form:</Text>
                <Text style={styles.detailValue}>{selectedReaction.dosageForm || 'Not specified'}</Text>
              </View>

              <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Outcomes:</Text>
              <Text style={styles.detailValue}>{Array.isArray(selectedReaction.outcomes) ? selectedReaction.outcomes.join(', ') : 'Not specified'}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Other:</Text>
                <Text style={styles.detailValue}>{selectedReaction.outcomesOthers || 'Not specified'}</Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Severity:</Text>
                <Chip
                  mode="flat"
                  style={{ backgroundColor: getSeverityColor(selectedReaction.severity) + '20' }}
                  textStyle={{ color: getSeverityColor(selectedReaction.severity) }}
                >
                  {selectedReaction.severity}
                </Chip>
              </View>
              <Text style={styles.sectionTitle}>Description</Text>
              <Text style={styles.description}>{selectedReaction.reactionDetails}</Text>

              {selectedReaction.image && (
                <>
                  <Text style={styles.sectionTitle}>Images</Text>
                  <ScrollView horizontal style={styles.imageContainer}>
                    <TouchableOpacity
                      onPress={() => {
                        setSelectedImage(`data:image/jpeg;base64,${selectedReaction.image}`);
                        setShowImageModal(true);
                      }}
                    >
                      <Image
                        source={{ uri: `data:image/jpeg;base64,${selectedReaction.image}` }}
                        style={styles.thumbnailImage}
                      />
                    </TouchableOpacity>
                  </ScrollView>
                </>
              )}

              <Text style={styles.sectionTitle}>Symptoms</Text>
              <View style={styles.symptomsContainer}>
                {selectedReaction.symptoms.map((symptom, index) => (
                  <Chip key={index} style={styles.symptomChip}>{symptom}</Chip>
                ))}
              </View>
              <Text style={styles.sectionTitle}>ADR Management</Text>
              <View style={styles.detailRow}>
                <Text style={styles.adrManagementLabel}>Hospitalization:</Text>
                <Text style={styles.detailValue}>{selectedReaction.hospitalized ? 'Yes' : 'No'}</Text>
                </View>
              <View style={styles.detailRow}>
                <Text style={styles.adrManagementLabel}>Discontinuation:</Text>
                <Text style={styles.detailValue}>{selectedReaction.discontinuation ? 'Yes' : 'No'}</Text>
                </View>
              <View style={styles.detailRow}>
                <Text style={styles.adrManagementLabel}>Exposure Time:</Text>
                <Text style={styles.detailValue}>{selectedReaction.timeAfterExposure || 'Not specified'}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.adrManagementLabel}>Treatment Given:</Text>
                <Text style={styles.detailValue}>{selectedReaction.treatmentGiven || 'Not specified'}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.adrManagementLabel}>Allergy Card:</Text>
                <Text style={styles.detailValue}>{selectedReaction.allergyCard ? 'Yes' : 'No'}</Text>
                </View>
              <Divider style={styles.divider} />

              <Text style={styles.sectionTitle}>How was ADR Managed?</Text>
              <Text style={styles.sectionTitle}>Hospitalization/Admission</Text>
              <SegmentedButtons
                value={hospitalized}
                onValueChange={setHospitalized}
                buttons={[
                  { value: 'yes', label: 'Yes' },
                  { value: 'no', label: 'No' },
                ]}
              />
              <Text style={styles.sectionTitle}>Discontinuation of Offending Drugs</Text>
              <SegmentedButtons
                value={discontinuation}
                onValueChange={setDiscontinuation}
                buttons={[
                  { value: 'yes', label: 'Yes' },
                  { value: 'no', label: 'No' },
                ]}
              />
              {discontinuation === 'yes' && (
                <TextInput
                  label="Time after exposure"
                  value={timeAfterExposure}
                  onChangeText={setTimeAfterExposure}
                  mode="outlined"
                  style={styles.notesInput}
                />
              )}
              <Text style={styles.sectionTitle}>Treatment Given</Text>
              <TextInput
                label="Specify"
                value={updateNote}
                onChangeText={setUpdateNote}
                mode="outlined"
                multiline
                numberOfLines={3}
                style={styles.notesInput}
              />
               <Text style={styles.sectionTitle}>Allergy Card</Text>
              <SegmentedButtons
                value={allergyCard}
                onValueChange={setallergyCard}
                buttons={[
                  { value: 'yes', label: 'Yes' },
                  { value: 'no', label: 'No' },
                ]}
              />
              <View style={styles.actionButtons}>
                <Button
                  mode="contained"
                  onPress={() => handleUpdateStatus('resolved')}
                  style={[styles.actionButton, { flex: 1, marginHorizontal: theme.spacing.xs }]}
                  disabled={selectedReaction.status === 'resolved'}
                >
                  Mark Resolved
                </Button>
                <Button
                  mode="contained"
                  onPress={() => handleUpdateStatus('pending')}
                  style={[styles.actionButton, { flex: 1, marginHorizontal: theme.spacing.xs }]}
                  disabled={selectedReaction.status === 'pending'}
                >
                  Mark Pending
                </Button>
                </View>
              </ScrollView>
            </>
          )}
        </Modal>
        <Modal
          visible={showImageModal}
          onDismiss={() => setShowImageModal(false)}
          contentContainerStyle={styles.fullImageModalContainer}
        >
          {selectedImage && (
            <View style={styles.fullImageContainer}>
              <Image
                source={{ uri: selectedImage }}
                style={styles.fullImage}
                resizeMode="contain"
              />
              <IconButton
                icon="close"
                size={24}
                onPress={() => setShowImageModal(false)}
                style={styles.closeButton}
              />
            </View>
          )}
        </Modal>
      </Portal>
    </View>
  );
};

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
  container: {
    ...globalStyles.container,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  reactionsList: {
    flex: 1,
  },
  reactionCard: {
    marginBottom: theme.spacing.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  caseId: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  drugName: {
    fontSize: 14,
    color: theme.colors.primary,
  },
  age: {
    fontSize: 14,
    color: theme.colors.text,
  },
  sex: {
    fontSize: 14,
    color: theme.colors.text,
  },
  contactNumber: {
    fontSize: 14,
    color: theme.colors.text,
},
  severityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  date: {
    fontSize: 12,
    color: theme.colors.placeholder,
  },
  description: {
    fontSize: 14,
    color: theme.colors.text,
  },
  modalContainer: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    margin: theme.spacing.md,
    borderRadius: theme.roundness,
    maxHeight: '80%',
  },
  modalScrollView: {
    flex: 1,
    paddingBottom: theme.spacing.lg,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  modalCloseButton: {
    margin: -8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  detailLabel: {
    width: 80,
    fontSize: 14,
    color: theme.colors.placeholder,
  },
  adrManagementLabel: {
    width: 120,
    fontSize: 14,
    color: theme.colors.placeholder,
  },
  detailValue: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.text,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  symptomsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.xs,
  },
  symptomChip: {
    marginBottom: theme.spacing.xs,
  },
  divider: {
    marginVertical: theme.spacing.md,
  },
  notesInput: {
    marginBottom: theme.spacing.md,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.spacing.md,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
  imageContainer: {
    flexDirection: 'row',
    marginBottom: theme.spacing.md,
  },
  thumbnailImage: {
    width: 80,
    height: 80,
    marginRight: theme.spacing.sm,
    borderRadius: theme.roundness,
  },
  fullImageModalContainer: {
    margin: 0,
    padding: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullImageContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.md,
  },
  fullImage: {
    width: '100%',
    height: '90%',
    resizeMode: 'contain',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
});