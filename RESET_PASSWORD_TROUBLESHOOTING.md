# Reset Password Navigation Troubleshooting Guide

## Issues Fixed

### 1. **Navigation Registration** ✅
- **Problem**: ResetPasswordScreen was not registered in the navigation stack
- **Solution**: Added ResetPasswordScreen to `src/navigation/index.tsx` auth stack
- **Result**: Screen is now properly registered and accessible

### 2. **Deep Linking Configuration** ✅
- **Problem**: No deep linking setup for handling email URLs
- **Solution**: Added linking configuration to NavigationContainer
- **Result**: <PERSON><PERSON> can now handle reset password URLs from emails

### 3. **URL Format Mismatch** ✅
- **Problem**: Backend email URLs didn't match frontend routing expectations
- **Solution**: Updated backend to generate correct URLs for React Native
- **Result**: Email links now use proper format for the app

### 4. **Token Parameter Handling** ✅
- **Problem**: Token wasn't being properly extracted from route parameters
- **Solution**: Updated ResetPasswordScreen to use useRoute hook correctly
- **Result**: Token is now properly extracted and processed

## Testing Steps

### **Step 1: Test Navigation Registration**
1. **Start your app** and go to the LoginScreen
2. **Look for the test button** "🧪 Test Reset Navigation" (temporary button added)
3. **Click the test button** - it should navigate to ResetPasswordScreen
4. **Check console logs** for debug information
5. **Expected Result**: ResetPasswordScreen should load with test token

### **Step 2: Test Email Flow**
1. **Click "Forgot Password?"** on LoginScreen
2. **Enter your email** and click "Send Reset Email"
3. **Check your email** for the reset message
4. **Click the reset link** in the email
5. **Expected Result**: Should open ResetPasswordScreen in your browser/app

### **Step 3: Test Token Verification**
1. **When ResetPasswordScreen loads**, check browser console
2. **Look for debug logs**:
   ```
   ResetPasswordScreen mounted
   Route params: {token: "your-token-here"}
   Token from params: your-token-here
   ```
3. **Expected Result**: Token should be properly extracted and verified

### **Step 4: Test Password Reset**
1. **Enter a new password** (minimum 6 characters)
2. **Confirm the password** (must match)
3. **Click "Reset Password"**
4. **Expected Result**: Success message and redirect to login

## Debug Information

### **Console Logs to Check**
When testing, look for these console messages:

```javascript
// Navigation test
"Testing navigation to ResetPasswordScreen"

// ResetPasswordScreen loading
"ResetPasswordScreen mounted"
"Route params: {token: 'your-token'}"
"Token from params: your-token"

// Token verification
"Token verification successful" or "Token verification failed"
```

### **Network Requests to Monitor**
1. **POST /api/forgot-password** - Password reset request
2. **GET /api/verify-reset-token/{token}** - Token verification
3. **POST /api/reset-password** - Password reset completion

## URL Formats

### **Web URL (Primary)**
```
http://localhost:19006/reset-password?token=your-token-here
```

### **Mobile Deep Link (Alternative)**
```
exp://localhost:19000/--/reset-password?token=your-token-here
```

## Common Issues & Solutions

### **Issue 1: "Screen not found" Error**
- **Cause**: Navigation not properly registered
- **Solution**: Restart the app after navigation changes
- **Check**: Ensure ResetPasswordScreen is imported and registered

### **Issue 2: Token is undefined**
- **Cause**: URL parameters not being parsed correctly
- **Solution**: Check URL format and deep linking configuration
- **Debug**: Look at console logs for route.params

### **Issue 3: Email link doesn't open app**
- **Cause**: Deep linking not configured for your platform
- **Solution**: Use web URL format or configure platform-specific deep linking
- **Workaround**: Copy URL and navigate manually

### **Issue 4: Token verification fails**
- **Cause**: Token expired or backend not running
- **Solution**: Check backend logs and token expiration (15 minutes)
- **Debug**: Test with fresh token

## Platform-Specific Notes

### **Web (Expo Web)**
- Uses standard web URLs: `http://localhost:19006/reset-password?token=...`
- Should work directly when clicking email links
- Opens in browser tab

### **Mobile (Expo Go)**
- Uses Expo deep links: `exp://localhost:19000/--/reset-password?token=...`
- May require Expo Go app to be running
- Alternative: Use web URL and copy/paste

### **Production**
- Update `FRONTEND_URL` environment variable
- Configure proper deep linking for your domain
- Test on actual devices

## Files Modified

### **Frontend**
1. **`src/navigation/index.tsx`**:
   - Added ResetPasswordScreen import
   - Added screen registration
   - Added deep linking configuration

2. **`src/screens/auth/ResetPasswordScreen.tsx`**:
   - Updated to use useRoute hook
   - Added debug logging
   - Improved token extraction

3. **`src/screens/auth/LoginScreen.tsx`**:
   - Added test navigation function
   - Added temporary test button

### **Backend**
1. **`backend/routes/passwordReset.js`**:
   - Updated URL generation
   - Added multiple URL formats
   - Enhanced email template

## Cleanup After Testing

### **Remove Test Elements**
Once everything is working, remove these test elements:

1. **In LoginScreen.tsx**, remove:
   ```typescript
   // Test function and button
   const handleTestResetNavigation = ...
   <Button>🧪 Test Reset Navigation</Button>
   ```

2. **In ResetPasswordScreen.tsx**, remove or reduce:
   ```typescript
   // Debug console logs
   console.log('ResetPasswordScreen mounted');
   console.log('Route params:', route.params);
   ```

## Expected Final Flow

1. **User clicks "Forgot Password?"** → Modal opens
2. **User enters email** → Validation passes
3. **User clicks "Send Reset Email"** → API call succeeds
4. **User receives email** → Professional template with reset link
5. **User clicks email link** → ResetPasswordScreen opens with token
6. **User enters new password** → Validation passes
7. **User clicks "Reset Password"** → Success and redirect to login
8. **User logs in** → Can use new password

## Troubleshooting Commands

### **Check Navigation State**
```javascript
// In React DevTools or console
console.log(navigation.getState());
```

### **Test URL Parsing**
```javascript
// Test URL manually
const testUrl = 'http://localhost:19006/reset-password?token=test123';
console.log(new URL(testUrl).searchParams.get('token'));
```

### **Verify Backend**
```bash
# Test backend endpoint
curl -X GET http://localhost:5000/api/verify-reset-token/test-token
```

## Success Indicators

✅ **Navigation works**: Test button successfully opens ResetPasswordScreen
✅ **Token extraction works**: Console shows correct token value
✅ **Token verification works**: API call succeeds and shows user email
✅ **Password reset works**: Can successfully reset password
✅ **Email links work**: Clicking email link opens the correct screen
✅ **Deep linking works**: URLs properly navigate to the right screen

If all these indicators pass, the forgot password feature is working correctly!
