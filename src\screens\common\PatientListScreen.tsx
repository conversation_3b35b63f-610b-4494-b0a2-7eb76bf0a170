import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, Alert } from 'react-native';
import {
  Text,
  Searchbar,
  Card,
  Button,
  Chip,
  Portal,
  Modal,
  ActivityIndicator,
  IconButton,
  Menu,
  Divider,
  List,
} from 'react-native-paper';
import { theme } from '../../theme';
import { useAuth } from '../../context/AuthContext';
import type { PatientFilters } from '../../types/patient';

export const PatientListScreen = ({ navigation }: any) => {
  const { fetchPatients } = useAuth();
  // Using any type here since the data from API has _id but PatientProfile expects id
  const [patients, setPatients] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFiltersModal, setShowFiltersModal] = useState(false);
  const [filters, setFilters] = useState<PatientFilters>({});
  const [menuVisible, setMenuVisible] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadPatients();
  }, [filters]);

  const loadPatients = async () => {
    try {
      setIsLoading(true);
      // fetchPatients now returns data with standardized id property
      const data = await fetchPatients();
      setPatients(data);
    } catch (error) {
      console.error('Error fetching patients:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadPatients();
    setRefreshing(false);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setTimeout(() => {
      loadPatients();
    }, 500);
  };

  const renderPatientCard = ({ item: patient }: { item: any }) => (
    <Card style={styles.patientCard} onPress={() => navigation.navigate('PatientProfile', { patient })}>
      <Card.Content>
        <View style={styles.cardHeader}>
          <View>
            <Text style={styles.patientName}>{patient.name}</Text>
            <Text style={styles.patientEmail}>{patient.email}</Text>
          </View>
        </View>
        <View style={styles.patientDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Hospital No:</Text>
            <Text style={styles.detailValue}>{patient.hospitalNo}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Age:</Text>
            <Text style={styles.detailValue}>
              {calculateAge(patient.dateOfBirth)} years
            </Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Sex:</Text>
            <Text style={styles.detailValue}>{patient.sex}</Text>
          </View>
          {patient.bloodType && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Blood Type:</Text>
              <Text style={styles.detailValue}>{patient.bloodType}</Text>
            </View>
          )}
        </View>

        <View style={styles.cardActions}>
        <Button
            mode="outlined"
            onPress={() => {
              // Use the standardized id property
              if (patient.id) {
                navigation.navigate('CounsellorChat', {
                  patientId: patient.id
                });
              } else {
                console.error('No valid patient ID found:', patient);
                Alert.alert(
                  "Error",
                  "Could not identify this patient. Please try again.",
                  [{ text: "OK" }]
                );
              }
            }}
            icon="chat"
            style={styles.actionButton}
          >
            Chat
          </Button>

          <Button
            mode="outlined"
            onPress={() => navigation.navigate('PatientProfile', {
              patient,
              activeTab: 'reports'
            })}
            icon="file-document"
            style={styles.actionButton}
          >
            Reports
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  if (isLoading && patients.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.title}>Patients</Text>
          <View style={styles.headerActions}>
            <IconButton
              icon="filter-variant"
              size={24}
              onPress={() => setShowFiltersModal(true)}
            />
            <Menu
              visible={menuVisible}
              onDismiss={() => setMenuVisible(false)}
              anchor={
                <IconButton
                  icon="dots-vertical"
                  size={24}
                  onPress={() => setMenuVisible(true)}
                />
              }
            >
              <Menu.Item
                onPress={() => {
                  setMenuVisible(false);
                }}
                title="Export List"
              />
              <Menu.Item
                onPress={() => {
                  setMenuVisible(false);
                }}
                title="Print List"
              />
            </Menu>
          </View>
        </View>
      </View>

      <Searchbar
        placeholder="Search patients..."
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchBar}
      />

      <FlatList
        data={patients}
        renderItem={renderPatientCard}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.list}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No patients found</Text>
          </View>
        )}
      />

      <Portal>
        <Modal
          visible={showFiltersModal}
          onDismiss={() => setShowFiltersModal(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Text style={styles.modalTitle}>Filter Patients</Text>

          <List.Section>
            <List.Subheader>Status</List.Subheader>
            <View style={styles.filterChips}>
              <Chip
                selected={filters.status === 'active'}
                onPress={() => setFilters({ ...filters, status: 'active' })}
                style={styles.filterChip}
              >
                Active
              </Chip>
              <Chip
                selected={filters.status === 'inactive'}
                onPress={() => setFilters({ ...filters, status: 'inactive' })}
                style={styles.filterChip}
              >
                Inactive
              </Chip>
            </View>
          </List.Section>

          <Divider />

          <View style={styles.modalButtons}>
            <Button
              mode="outlined"
              onPress={() => {
                setFilters({});
                setShowFiltersModal(false);
              }}
              style={styles.modalButton}
            >
              Clear Filters
            </Button>
            <Button
              mode="contained"
              onPress={() => setShowFiltersModal(false)}
              style={styles.modalButton}
            >
              Apply
            </Button>
          </View>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchBar: {
    margin: theme.spacing.sm,
    elevation: 0,
  },
  list: {
    padding: theme.spacing.sm,
  },
  patientCard: {
    marginBottom: theme.spacing.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  patientName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  patientEmail: {
    fontSize: 14,
    color: theme.colors.placeholder,
  },
  patientDetails: {
    marginBottom: theme.spacing.md,
  },
  detailRow: {
    flexDirection: 'row',
    marginVertical: theme.spacing.xs,
  },
  detailLabel: {
    width: 100,
    fontSize: 14,
    color: theme.colors.placeholder,
  },
  detailValue: {
    fontSize: 14,
    color: theme.colors.text,
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    gap: theme.spacing.sm,
  },
  actionButton: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  emptyText: {
    fontSize: 16,
    color: theme.colors.placeholder,
  },
  modalContainer: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    margin: theme.spacing.md,
    borderRadius: theme.roundness,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: theme.spacing.md,
    color: theme.colors.primary,
  },
  filterChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
    padding: theme.spacing.sm,
  },
  filterChip: {
    marginBottom: theme.spacing.xs,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: theme.spacing.md,
  },
  modalButton: {
    marginLeft: theme.spacing.sm,
  },
});
