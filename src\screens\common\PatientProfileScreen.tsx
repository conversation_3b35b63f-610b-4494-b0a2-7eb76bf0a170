import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Image, TouchableOpacity, Dimensions, Alert } from 'react-native';
import {
  Text,
  Surface,
  Button,
  TextInput,
  Portal,
  Modal,
  ActivityIndicator,
  List,
  Divider,
  IconButton,
  SegmentedButtons,
  Card,
  Chip,
} from 'react-native-paper';
import axios from 'axios';
import { theme, styles as globalStyles } from '../../theme';
import { usePatient } from '../../context/PatientContext';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import type { UpdatePatientData } from '../../types/patient';
import type { DrugReaction, ReactionSeverity } from '../../types/drugReaction';
import { Dropdown } from 'react-native-element-dropdown';
import { DatePickerModal } from 'react-native-paper-dates';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const PatientProfileScreen = ({ route, navigation }: any) => {
  const { patient } = route.params;
  const { user } = useAuth();

  // Debug: Log patient data to understand structure
  useEffect(() => {
    console.log('Patient data received:', patient);
    console.log('Patient keys:', patient ? Object.keys(patient) : 'No patient');
    if (patient) {
      console.log('Date of Birth:', patient.dateOfBirth);
      console.log('Weight:', patient.weight);
      console.log('Ethnicity:', patient.ethnicity);
      console.log('Prior Counsel:', patient.priorCounsel);
    }
  }, [patient]);
  const {
    updatePatient,
    getMedicalHistory,
    getPrescriptions,
    getAppointments,
    getVitals,
    isLoading,
    error
  } = usePatient();

  // Dropdown options
  const sexOptions = [
    { label: 'Male', value: 'Male' },
    { label: 'Female', value: 'Female' },
  ];

  const counselOptions = [
    { label: 'Yes', value: 'Yes' },
    { label: 'No', value: 'No' },
  ];

  const ethnicityOptions = [
    { label: 'Brahmin/Chhetri', value: 'Brahmin/Chhetri' },
    { label: 'Terai/Madhesi', value: 'Terai/Madhesi' },
    { label: 'Dalits', value: 'Dalits' },
    { label: 'Newar', value: 'Newar' },
    { label: 'Janajati', value: 'Janajati' },
    { label: 'Muslim', value: 'Muslim' },
  ];

  const [showEditModal, setShowEditModal] = useState(false);
  const [editData, setEditData] = useState<UpdatePatientData>({});
  const [activeTab, setActiveTab] = useState(route.params?.activeTab || 'info');
  const [reactions, setReactions] = useState<DrugReaction[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [showImageModal, setShowImageModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Calculate age function
  const calculateAge = (birthDate: Date): number => {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      try {
        const isoDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000)
          .toISOString()
          .split('T')[0];
        const age = calculateAge(date);
        setEditData({ ...editData, dateOfBirth: isoDate, age });
      } catch (error) {
        console.error('Error handling date change:', error);
        Alert.alert('Error', 'Invalid date selected. Please try again.');
      }
    }
    setShowDatePicker(false);
  };

  const getSeverityColor = (severity: ReactionSeverity) => {
    switch (severity) {
      case 'mild':
        return theme.colors.primary;
      case 'moderate':
        return '#FFA500';
      case 'severe':
        return theme.colors.error;
      default:
        return theme.colors.text;
    }
  };

  useEffect(() => {
    const loadReactions = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/api/drug-reactions`);
        // Check for both patient._id and patient.id to ensure compatibility
        const patientIdentifier = patient._id || patient.id;

        const filteredReactions = response.data.filter(
          (reaction: DrugReaction) => {
            // Check if the reaction has a user and if that user's ID matches the patient's ID
            return reaction.user && reaction.user._id === patientIdentifier;
          }
        );

        console.log(`Found ${filteredReactions.length} reactions for patient ID: ${patientIdentifier}`);
        setReactions(filteredReactions);
      } catch (error) {
        console.error('Failed to load reactions:', error);
      }
    };

    // Check for both patient._id and patient.id to ensure we can fetch reactions
    if (patient?._id || patient?.id) {
      loadReactions();
    } else {
      console.warn('No valid patient ID found for fetching reactions');
    }
  }, [patient]);

  const handleUpdate = async () => {
    try {
      // Get auth token
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // If name components are updated, combine them
      if (editData.firstName || editData.lastName) {
        const firstName = editData.firstName || patient.firstName;
        const lastName = editData.lastName || patient.lastName;
        editData.name = `${firstName} ${lastName}`;
      }

      // Use the correct patient ID - try _id first, then id
      const patientId = patient._id || patient.id;
      console.log('Updating patient with ID:', patientId);
      console.log('Update data:', editData);

      const response = await fetch(`${API_BASE_URL}/api/patients/update-profile/${patientId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(editData),
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('Error response:', errorText);

        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch (parseError) {
          console.log('Failed to parse error response as JSON:', parseError);
          throw new Error(`Server error: ${response.status} - ${errorText}`);
        }

        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      const responseData = await response.json();
      console.log('Update response:', responseData);

      // Update local state through context
      await updatePatient(patientId, editData);
      setShowEditModal(false);
      setEditData({});
      Alert.alert('Success', 'Patient information updated successfully');
    } catch (error) {
      console.error('Failed to update patient:', error);
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to update patient');
    }
  };

  // Helper function to safely get patient data
  const getPatientValue = (field: string, fallback: string = 'Not specified') => {
    const value = patient?.[field];

    // Handle different data types
    if (value === null || value === undefined) {
      return fallback;
    }

    // Handle empty strings
    if (typeof value === 'string' && value.trim() === '') {
      return fallback;
    }

    // Handle numbers (including 0)
    if (typeof value === 'number') {
      return value.toString();
    }

    // Handle dates
    if (field.includes('date') || field.includes('Date')) {
      try {
        return new Date(value).toLocaleDateString();
      } catch (error) {
        return fallback;
      }
    }

    return value;
  };

  if (!patient) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Patient data not available</Text>
      </View>
    );
  }

  const canEdit = user?.role === 'doctor' || user?.role === 'drug-counsellor';

  // Function to initialize edit form with patient data
  const initializeEditForm = () => {
    setEditData({
      hospitalNo: patient.hospitalNo || '',
      firstName: patient.firstName || '',
      lastName: patient.lastName || '',
      dateOfBirth: patient.dateOfBirth || '',
      age: patient.age || 0,
      sex: patient.sex || '',
      weight: patient.weight || 0,
      contactNumber: patient.contactNumber || '',
      ethnicity: patient.ethnicity || '',
      priorCounsel: patient.priorCounsel || '',
    });
  };

  return (
    <View style={styles.container}>
      <Surface style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Patient Profile</Text>
          {canEdit && (
            <IconButton
              icon="pencil"
              size={24}
              onPress={() => {
                initializeEditForm();
                setShowEditModal(true);
              }}
            />
          )}
        </View>
      </Surface>

      <SegmentedButtons
        value={activeTab}
        onValueChange={setActiveTab}
        buttons={[
          { value: 'info', label: 'Info' },
          { value: 'reports', label: 'Reports' },
          { value: 'history', label: 'History' },
          { value: 'prescriptions', label: 'Prescriptions' },
        ]}
        style={styles.tabButtons}
      />

      <ScrollView style={styles.content}>
        {activeTab === 'info' && (
          <Card style={styles.card}>
            <Card.Content>
              <List.Item
                title="Name"
                description={getPatientValue('name') !== 'Not specified' ? getPatientValue('name') :
                  `${getPatientValue('firstName', '')} ${getPatientValue('lastName', '')}`.trim() || 'Not specified'}
                left={props => <List.Icon {...props} icon="account" />}
              />
              <Divider />
              <List.Item
                title="Email"
                description={getPatientValue('email')}
                left={props => <List.Icon {...props} icon="email" />}
              />
              <Divider />
              <List.Item
                title="Date of Birth"
                description={getPatientValue('dateOfBirth')}
                left={props => <List.Icon {...props} icon="calendar" />}
              />
              <Divider />
              <List.Item
                title="Hospital Number"
                description={getPatientValue('hospitalNo')}
                left={props => <List.Icon {...props} icon="hospital" />}
              />
              <Divider />
              <List.Item
                title="First Name"
                description={getPatientValue('firstName')}
                left={props => <List.Icon {...props} icon="account" />}
              />
              <Divider />
              <List.Item
                title="Last Name"
                description={getPatientValue('lastName')}
                left={props => <List.Icon {...props} icon="account" />}
              />
              <Divider />
              <List.Item
                title="Age"
                description={getPatientValue('age')}
                left={props => <List.Icon {...props} icon="human" />}
              />
              <Divider />
              <List.Item
                title="Sex"
                description={getPatientValue('sex')}
                left={props => <List.Icon {...props} icon="gender-male-female" />}
              />
              <Divider />
              <List.Item
                title="Weight"
                description={patient.weight !== undefined && patient.weight !== null ? `${patient.weight} kg` : 'Not specified'}
                left={props => <List.Icon {...props} icon="weight" />}
              />
              <Divider />
              <List.Item
                title="Contact Number"
                description={getPatientValue('contactNumber')}
                left={props => <List.Icon {...props} icon="phone" />}
              />
              <Divider />
              <List.Item
                title="Ethnicity"
                description={getPatientValue('ethnicity')}
                left={props => <List.Icon {...props} icon="earth" />}
              />
              <Divider />
              {getPatientValue('sex') !== 'Male' && getPatientValue('sex') !== 'Not specified' && (
                <>
                  <List.Item
                    title="Pregnant Status"
                    description={getPatientValue('pregnant')}
                    left={props => <List.Icon {...props} icon="baby-face" />}
                  />
                  <Divider />
                </>
              )}
              <List.Item
                title="Prior Counseling"
                description={getPatientValue('priorCounsel')}
                left={props => <List.Icon {...props} icon="chat" />}
              />
              <List.Item
                title="Registration Date"
                description={patient.createdAt ? (() => {
                  try {
                    return new Date(patient.createdAt).toLocaleDateString();
                  } catch (error) {
                    return 'Invalid date';
                  }
                })() : 'Not specified'}
                left={props => <List.Icon {...props} icon="calendar" />}
              />
            </Card.Content>
          </Card>
        )}

        {activeTab === 'history' && (
          <Card style={styles.card}>
            <Card.Content>
              <Text>Medical History</Text>
            </Card.Content>
          </Card>
        )}
        {activeTab === 'reports' && (
          <View style={styles.reportsContainer}>
            {reactions.map((reaction) => (
              <Card key={reaction._id} style={styles.reactionCard}>
                <Card.Content>
                  <View style={styles.cardHeader}>
                    <Text style={styles.drugName}>{reaction.drugName}</Text>
                    <Chip
                      mode="outlined"
                      textStyle={{ color: getSeverityColor(reaction.severity) }}
                    >
                      {reaction.severity}
                    </Chip>
                  </View>

                  <List.Item
                    title="Reaction Details"
                    description={reaction.reactionDetails}
                    left={props => <List.Icon {...props} icon="alert" />}
                  />
                  <Divider />

                  <List.Item
                    title="Status"
                    description={reaction.status}
                    left={props => <List.Icon {...props} icon="information" />}
                  />
                  <Divider />

                  <List.Item
                    title="Reported Date"
                    description={new Date(reaction.dateReported).toLocaleDateString()}
                    left={props => <List.Icon {...props} icon="calendar" />}
                  />
                  <Divider />

                  <List.Item
                    title="Date Started"
                    description={reaction.dateStarted ? new Date(reaction.dateStarted).toLocaleDateString() : 'Not specified'}
                    left={props => <List.Icon {...props} icon="calendar-start" />}
                  />
                  <Divider />

                  <List.Item
                    title="Date Stopped"
                    description={reaction.dateStopped ? new Date(reaction.dateStopped).toLocaleDateString() : 'Not specified'}
                    left={props => <List.Icon {...props} icon="calendar-end" />}
                  />
                  <Divider />

                  <List.Item
                    title="Daily Dose"
                    description={reaction.dailyDose || 'Not specified'}
                    left={props => <List.Icon {...props} icon="pill" />}
                  />
                  <Divider />

                  <List.Item
                    title="Route"
                    description={reaction.routeOfAdministration || 'Not specified'}
                    left={props => <List.Icon {...props} icon="routes" />}
                  />
                  <Divider />

                  <List.Item
                    title="Dosage Form"
                    description={reaction.dosageForm || 'Not specified'}
                    left={props => <List.Icon {...props} icon="format-list-bulleted" />}
                  />
                  <Divider />

                  <List.Item
                    title="Hospitalized"
                    description={reaction.hospitalized ? 'Yes' : 'No'}
                    left={props => <List.Icon {...props} icon="hospital" />}
                  />
                  <Divider />
                  <List.Item
                    title="Allergy Card"
                    description={reaction.allergyCard ? 'Yes' : 'No'}
                    left={props => <List.Icon {...props} icon="hospital" />}
                  />
                  <Divider />

                  <List.Item
                    title="Discontinuation"
                    description={reaction.discontinuation ? 'Yes' : 'No'}
                    left={props => <List.Icon {...props} icon="cancel" />}
                  />
                  <Divider />

                  <List.Item
                    title="Time After Exposure"
                    description={reaction.timeAfterExposure || 'Not specified'}
                    left={props => <List.Icon {...props} icon="clock" />}
                  />
                  <Divider />

                  <List.Item
                    title="Treatment Given"
                    description={reaction.treatmentGiven || 'Not specified'}
                    left={props => <List.Icon {...props} icon="medical-bag" />}
                  />
                  <Divider />

                  {reaction.image && (
                    <List.Item
                      title="Image"
                      left={props => <List.Icon {...props} icon="image" />}
                      right={() => (
                        <TouchableOpacity
                          onPress={() => {
                            setSelectedImage(`data:image/jpeg;base64,${reaction.image}`);
                            setShowImageModal(true);
                          }}
                        >
                          <Image
                            source={{ uri: `data:image/jpeg;base64,${reaction.image}` }}
                            style={styles.thumbnailImage}
                          />
                        </TouchableOpacity>
                      )}
                    />
                  )}
                </Card.Content>
              </Card>
            ))}

          </View>
        )}

        <Portal>
          <Modal
            visible={showImageModal}
            onDismiss={() => setShowImageModal(false)}
            contentContainerStyle={styles.fullImageModalContainer}
          >
            {selectedImage && (
              <View style={styles.fullImageContainer}>
                <Image
                  source={{ uri: selectedImage }}
                  style={styles.fullImage}
                  resizeMode="contain"
                />
                <IconButton
                  icon="close"
                  size={24}
                  onPress={() => setShowImageModal(false)}
                  style={styles.closeButton}
                />
              </View>
            )}
          </Modal>
        </Portal>

        {activeTab === 'prescriptions' && (
          <Card style={styles.card}>
            <Card.Content>
              <Text>Prescriptions</Text>
            </Card.Content>
          </Card>
        )}
      </ScrollView>

      <Portal>
        <Modal
          visible={showEditModal}
          onDismiss={() => {
            setShowEditModal(false);
            setEditData({});
          }}
          contentContainerStyle={styles.modalContainer}
        >
          <Text style={styles.modalTitle}>Edit Patient Information</Text>

          <ScrollView>
            <TextInput
              label="Hospital Number"
              value={editData.hospitalNo !== undefined ? editData.hospitalNo : (patient.hospitalNo || '')}
              onChangeText={(text) => setEditData({ ...editData, hospitalNo: text })}
              style={styles.input}
              mode="outlined"
            />

            <TextInput
              label="First Name"
              value={editData.firstName !== undefined ? editData.firstName : (patient.firstName || '')}
              onChangeText={(text) => setEditData({ ...editData, firstName: text })}
              style={styles.input}
              mode="outlined"
            />

            <TextInput
              label="Last Name"
              value={editData.lastName !== undefined ? editData.lastName : (patient.lastName || '')}
              onChangeText={(text) => setEditData({ ...editData, lastName: text })}
              style={styles.input}
              mode="outlined"
            />

            {/* Date of Birth Picker */}
            <TouchableOpacity onPress={() => setShowDatePicker(true)}>
              <TextInput
                label="Date of Birth"
                value={(() => {
                  try {
                    const dateToShow = editData.dateOfBirth || patient.dateOfBirth;
                    return dateToShow ? new Date(dateToShow).toLocaleDateString('en-US') : '';
                  } catch (error) {
                    console.error('Error formatting date:', error);
                    return '';
                  }
                })()}
                style={styles.input}
                mode="outlined"
                left={<TextInput.Icon icon="calendar" color={theme.colors.primary} />}
                editable={false}
                pointerEvents="none"
              />
            </TouchableOpacity>

            <DatePickerModal
              mode="single"
              visible={showDatePicker}
              onDismiss={() => setShowDatePicker(false)}
              date={(() => {
                try {
                  const dateToUse = editData.dateOfBirth || patient.dateOfBirth;
                  return dateToUse ? new Date(dateToUse) : new Date();
                } catch (error) {
                  console.error('Error parsing date for picker:', error);
                  return new Date();
                }
              })()}
              onConfirm={({ date }) => handleDateChange(date)}
              locale="en"
              validRange={{
                startDate: new Date(1900, 0, 1),
                endDate: new Date(),
              }}
            />

            <TextInput
              label="Age"
              value={String(editData.age !== undefined ? editData.age : (patient.age || ''))}
              style={styles.input}
              mode="outlined"
              keyboardType="numeric"
              editable={false}
            />

            {/* Sex Dropdown */}
            <Text style={styles.label}>Sex</Text>
            <Dropdown
              style={styles.dropdown}
              data={sexOptions}
              labelField="label"
              valueField="value"
              placeholder="Select Sex"
              value={editData.sex !== undefined ? editData.sex : (patient.sex || '')}
              onChange={(item) => setEditData({ ...editData, sex: item.value })}
            />

            <TextInput
              label="Weight (kg)"
              value={String(editData.weight !== undefined ? editData.weight : (patient.weight || ''))}
              onChangeText={(text) => setEditData({ ...editData, weight: Number(text) || 0 })}
              style={styles.input}
              mode="outlined"
              keyboardType="numeric"
            />

            <TextInput
              label="Contact Number"
              value={editData.contactNumber !== undefined ? editData.contactNumber : (patient.contactNumber || '')}
              onChangeText={(text) => setEditData({ ...editData, contactNumber: text })}
              style={styles.input}
              mode="outlined"
              keyboardType="phone-pad"
            />

            {/* Ethnicity Dropdown */}
            <Text style={styles.label}>Ethnicity</Text>
            <Dropdown
              style={styles.dropdown}
              data={ethnicityOptions}
              labelField="label"
              valueField="value"
              placeholder="Select Ethnicity"
              value={editData.ethnicity !== undefined ? editData.ethnicity : (patient.ethnicity || '')}
              onChange={(item) => setEditData({ ...editData, ethnicity: item.value })}
            />

            {/* Prior Counseling Dropdown */}
            <Text style={styles.label}>Prior Counseling (Yes/No)</Text>
            <Dropdown
              style={styles.dropdown}
              data={counselOptions}
              labelField="label"
              valueField="value"
              placeholder="Select option"
              value={editData.priorCounsel !== undefined ? editData.priorCounsel : (patient.priorCounsel || '')}
              onChange={(item) => setEditData({ ...editData, priorCounsel: item.value })}
            />


            <View style={styles.modalButtons}>
              <Button
                mode="outlined"
                onPress={() => {
                  setShowEditModal(false);
                  setEditData({});
                }}
                style={styles.modalButton}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={handleUpdate}
                style={styles.modalButton}
              >
                Save Changes
              </Button>
            </View>
          </ScrollView>
        </Modal>
      </Portal>
    </View>
  );
};

// Get dimensions for responsive styling
const { width: windowWidth } = Dimensions.get('window');

const styles = StyleSheet.create({
  reportsContainer: {
    padding: theme.spacing.sm,
    width: '100%',
  },
  reactionCard: {
    marginBottom: theme.spacing.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  drugName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  date: {
    fontSize: 12,
    color: theme.colors.placeholder,
    marginBottom: theme.spacing.sm,
  },
  description: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  thumbnailImage: {
    width: 100,
    height: 100,
    borderRadius: theme.roundness,
  },
  fullImageModalContainer: {
    margin: 0,
    padding: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: windowWidth,
  },
  fullImageContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.md,
  },
  fullImage: {
    width: '100%',
    height: '90%',
    resizeMode: 'contain',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  reactionItem: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.sm,
    borderWidth: 1,
    borderColor: theme.colors.outline,
    borderRadius: theme.roundness,
  },
  reactionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: theme.spacing.xs,
  },
  reactionImage: {
    width: '100%',
    height: 200,
    marginTop: theme.spacing.sm,
    resizeMode: 'contain',
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: theme.colors.error,
    fontSize: 16,
  },
  header: {
    elevation: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.sm,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginLeft: theme.spacing.sm,
  },
  tabButtons: {
    margin: theme.spacing.sm,
  },
  content: {
    flex: 1,
  },
  card: {
    margin: theme.spacing.sm,
  },
  modalContainer: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    margin: theme.spacing.md,
    borderRadius: theme.roundness,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: theme.spacing.md,
    color: theme.colors.primary,
  },
  input: {
    marginBottom: theme.spacing.sm,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: theme.spacing.md,
  },
  modalButton: {
    marginLeft: theme.spacing.sm,
  },
  dropdown: {
    height: 50,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 5,
    paddingHorizontal: 10,
    marginBottom: 10,
    justifyContent: 'center'
  },
  label: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: 5
  },
  dropdownLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
    color: '#333', // You can tweak this color to fit your theme
  },

});
