# Reset Password Testing Guide

## Issue Resolution

### **Problem Identified** ✅
The error `GET http://127.0.0.1:5000/api/verify-reset-token/test-token-123 400 (Bad Request)` was occurring because:

1. **Fake Test Token**: The test was using a hardcoded fake token `test-token-123`
2. **No Valid Token**: The backend correctly rejected the invalid token
3. **Expected Behavior**: This is actually correct security behavior

### **Solutions Implemented** ✅

#### **1. Real Test Token Creation**
- Added `/api/create-test-token` endpoint to backend
- Creates valid tokens for testing purposes
- Only available in development mode

#### **2. Improved Test Function**
- Updated LoginScreen test button to create real tokens
- Added proper error handling and user feedback
- Uses test utilities for cleaner code

#### **3. Enhanced Error Handling**
- Better error messages in ResetPasswordScreen
- Detailed console logging for debugging
- Clear feedback for different error scenarios

## Testing Steps

### **Step 1: Test with Real Token (Recommended)**

1. **Start your backend server**:
   ```bash
   cd backend
   npm start
   ```

2. **Start your frontend**:
   ```bash
   npm start
   ```

3. **Go to LoginScreen** and click the **"🧪 Test Reset Navigation"** button

4. **Expected Console Output**:
   ```
   🧪 Testing navigation with real token...
   Creating test token for email: <EMAIL>
   Test token created successfully: {token: "real-token-here", email: "<EMAIL>"}
   Navigating to ResetPassword screen with token: real-token-here
   ResetPasswordScreen mounted
   Route params: {token: "real-token-here"}
   Token from params: real-token-here
   Verifying token with backend: real-token-here
   Token verification successful: {message: "Token is valid", email: "<EMAIL>"}
   ```

5. **Expected Result**: ResetPasswordScreen loads successfully with valid token

### **Step 2: Test Email Flow**

1. **Use "Forgot Password?" feature**:
   - Click "Forgot Password?" on LoginScreen
   - Enter a real email address
   - Click "Send Reset Email"

2. **Check your email** for the reset message

3. **Click the reset link** in the email

4. **Expected Result**: Opens ResetPasswordScreen with real token from email

### **Step 3: Test Complete Password Reset**

1. **On ResetPasswordScreen**:
   - Enter a new password (minimum 6 characters)
   - Confirm the password
   - Click "Reset Password"

2. **Expected Result**: Success message and redirect to login

3. **Test login** with the new password

## Console Output Guide

### **Successful Flow**
```
🧪 Testing navigation with real token...
Creating test token for email: <EMAIL>
Test token created: abc123def456...
Navigating to ResetPasswordScreen with real token
ResetPasswordScreen mounted
Route params: {token: "abc123def456..."}
Token from params: abc123def456...
Verifying token with backend: abc123def456...
Token verification successful: {message: "Token is valid", email: "<EMAIL>"}
```

### **Backend Not Running**
```
Failed to create test token: Error: Network Error
Test Error: Failed to create test token. Make sure the backend is running.
```

### **Invalid Token (Expected for fake tokens)**
```
Token verification failed: Error: Request failed with status code 400
Backend error message: Invalid reset token
```

## API Endpoints

### **Test Token Creation** (Development Only)
```
POST /api/create-test-token
Body: { "email": "<EMAIL>" }
Response: {
  "message": "Test token created successfully",
  "token": "abc123def456...",
  "email": "<EMAIL>",
  "expiresIn": "15 minutes"
}
```

### **Token Verification**
```
GET /api/verify-reset-token/{token}
Response: {
  "message": "Token is valid",
  "email": "<EMAIL>"
}
```

### **Password Reset**
```
POST /api/reset-password
Body: {
  "token": "abc123def456...",
  "newPassword": "newpassword123"
}
Response: {
  "message": "Password has been reset successfully"
}
```

## Troubleshooting

### **Issue: Backend Not Running**
**Symptoms**: Network errors, connection refused
**Solution**: 
```bash
cd backend
npm start
```

### **Issue: Invalid Token Error**
**Symptoms**: 400 Bad Request on token verification
**Causes**:
1. Using fake/hardcoded token (expected)
2. Token expired (15-minute limit)
3. Token already used

**Solutions**:
1. Use the test button to create real tokens
2. Create fresh tokens for testing
3. Check backend logs for token status

### **Issue: Navigation Not Working**
**Symptoms**: Screen doesn't load, blank screen
**Solutions**:
1. Check that ResetPasswordScreen is registered in navigation
2. Restart the app after navigation changes
3. Check console for navigation errors

### **Issue: Token Not Extracted**
**Symptoms**: `Token from params: undefined`
**Solutions**:
1. Check URL format in email
2. Verify deep linking configuration
3. Test with manual navigation first

## Test Utilities

### **Available Test Functions**
```typescript
import { testUtils } from '../utils/testUtils';

// Create test token
const tokenData = await testUtils.createTestToken('<EMAIL>');

// Verify token
const verification = await testUtils.verifyTestToken(token);

// Test complete flow
const result = await testUtils.testPasswordResetFlow();

// Check backend status
const status = await testUtils.checkBackendStatus();

// Test navigation
await testUtils.testNavigationWithRealToken(navigation);
```

## Expected Results Summary

### **✅ Working Correctly**
- Test button creates real token and navigates successfully
- Token verification succeeds with valid tokens
- Password reset completes successfully
- Email links work and open ResetPasswordScreen
- Console shows detailed debug information

### **❌ Error Cases (Expected)**
- Fake tokens return 400 Bad Request (correct security behavior)
- Expired tokens are rejected (correct security behavior)
- Used tokens are rejected (correct security behavior)

## Production Notes

### **Remove Test Elements**
Before deploying to production:

1. **Remove test button** from LoginScreen
2. **Remove test endpoint** from backend (automatically disabled in production)
3. **Remove excessive console logging**
4. **Update environment variables** for production URLs

### **Security Considerations**
- Test endpoint only works in development
- Real tokens expire in 15 minutes
- Tokens are single-use only
- Email enumeration protection is maintained

The 400 error you saw was actually **correct behavior** - the system properly rejected an invalid token. Now with real token creation, the flow should work perfectly!
