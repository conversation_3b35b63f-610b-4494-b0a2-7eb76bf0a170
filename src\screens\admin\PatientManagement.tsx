import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, Dimensions, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  Text,
  Button,
  Card,
  Portal,
  Modal,
  IconButton,
  Searchbar,
  TextInput,
  Divider,
  ActivityIndicator,
  Dialog,
  Paragraph,
  Avatar,
  useTheme
} from 'react-native-paper';
import { theme, styles as globalStyles } from '../../theme';
import { useAuth, API_BASE_URL } from '../../context/AuthContext';
import type { Patient } from '../../types';
import { transformPatients } from '../../utils/dataTransformers';
import { searchUsers } from '../../utils/searchUtils';

// Simple responsive breakpoints - same as in StaffManagement.tsx
const SCREEN_BREAKPOINT_TABLET = 768;

export const PatientManagement = ({ navigation }: any) => {
  const { fetchPatients } = useAuth();
  const paperTheme = useTheme();

  // Get screen width for responsive design
  const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);

  const [searchQuery, setSearchQuery] = useState('');
  const [showEditModal, setShowEditModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [patientData, setPatientData] = useState<Patient[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [formError, setFormError] = useState('');

  // State for delete confirmation dialog
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [patientToDelete, setPatientToDelete] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    age: '',
    sex: '',
    contactNumber: '',
    hospitalNo: ''
  });

  const fetchPatientData = async () => {
    try {
      setIsLoading(true);
      const data = await fetchPatients();

      // Ensure all patients have a valid id property
      const patientsWithIds = transformPatients(data);

      // Log the transformed data for debugging
      console.log('Transformed patient data:', patientsWithIds.map(p => ({ id: p.id, name: p.firstName + ' ' + p.lastName })));

      setPatientData(patientsWithIds);
    } catch (error) {
      console.error('Error fetching patients:', error);
      setFormError('Failed to load patient data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPatientData();
  }, []);

  // Add event listener for screen dimension changes
  useEffect(() => {
    const updateDimensions = () => {
      setScreenWidth(Dimensions.get('window').width);
    };

    // Set up event listener for dimension changes
    const dimensionsSubscription = Dimensions.addEventListener('change', updateDimensions);

    // Clean up event listener on component unmount
    return () => {
      dimensionsSubscription.remove();
    };
  }, []);

  // Enhanced search using the search utility function
  const filteredPatients = searchUsers(patientData, searchQuery, ['hospitalNo', 'contactNumber']);

  const handleEditPatient = (patient: Patient) => {
    // Check if patient exists
    if (!patient) {
      console.error('handleEditPatient called with null or undefined patient');
      Alert.alert('Error', 'Cannot edit patient: Invalid patient data');
      return;
    }

    // Generate a fallback ID if needed
    let patientWithId = { ...patient };
    if (!patientWithId.id) {
      console.warn('Patient missing ID, using MongoDB _id or generating fallback ID:', patient);

      // Use MongoDB _id if available, otherwise generate a fallback ID
      const fallbackId = patientWithId._id ||
        `${patient.hospitalNo || ''}-${patient.email || ''}-${Date.now()}`;

      patientWithId.id = fallbackId;
    }

    // Verify the patient has an ID before proceeding
    if (!patientWithId.id) {
      console.error('Failed to generate fallback ID for patient:', patient);
      Alert.alert('Error', 'Cannot edit patient: Unable to generate ID');
      return;
    }

    // Set the selected patient and form data
    setSelectedPatient(patientWithId);
    setFormData({
      hospitalNo: patientWithId.hospitalNo || '',
      firstName: patientWithId.firstName || '',
      lastName: patientWithId.lastName || '',
      email: patientWithId.email || '',
      age: patientWithId.age?.toString() || '',
      sex: patientWithId.sex || '',
      contactNumber: patientWithId.contactNumber || ''
    });

    // Verify the patient was set correctly
    console.log('Selected patient for editing:', patientWithId);

    setShowEditModal(true);
  };

  const handleUpdatePatient = async () => {
    try {
      setIsLoading(true);
      setFormError('');

      // Double-check that we have a selected patient with a valid ID
      if (!selectedPatient) {
        console.error('handleUpdatePatient: No patient selected');
        throw new Error('No patient selected');
      }

      if (!selectedPatient.id) {
        console.error('handleUpdatePatient: Selected patient has no ID', selectedPatient);
        throw new Error('Selected patient has no ID');
      }

      console.log('Updating patient with ID:', selectedPatient.id);

      // Validate form data
      if (!formData.firstName || !formData.lastName || !formData.email) {
        throw new Error('First name, last name, and email are required');
      }

      if (formData.age && isNaN(Number(formData.age))) {
        throw new Error('Age must be a number');
      }

      // Get the auth token
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_BASE_URL}/api/patients/update-profile/${selectedPatient.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          age: Number(formData.age),
          sex: formData.sex,
          contactNumber: formData.contactNumber,
          hospitalNo: formData.hospitalNo
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update patient');
      }

      await fetchPatientData();
      setShowEditModal(false);
      Alert.alert('Success', 'Patient updated successfully');
    } catch (error) {
      console.error('Error updating patient:', error);
      setFormError(error instanceof Error ? error.message : 'Failed to update patient');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to show the delete confirmation dialog
  const handleDeletePatient = (patientId: string) => {
    if (!patientId) {
      console.error('handleDeletePatient called with invalid ID:', patientId);
      Alert.alert('Error', 'Cannot delete patient: Invalid ID');
      return;
    }

    console.log('Showing delete confirmation for patient with ID:', patientId);
    setPatientToDelete(patientId);
    setDeleteDialogVisible(true);
  };

  // Function to handle delete confirmation
  const handleDeleteConfirm = () => {
    if (!patientToDelete) {
      console.error('handleDeleteConfirm called with no patient ID set');
      Alert.alert('Error', 'No patient selected for deletion');
      setDeleteDialogVisible(false);
      return;
    }

    deletePatient(patientToDelete);
    setDeleteDialogVisible(false);
    setPatientToDelete(null);
  };

  // Function to dismiss the delete dialog
  const hideDeleteDialog = () => {
    setDeleteDialogVisible(false);
    setPatientToDelete(null);
  };

  // Function to actually delete the patient
  const deletePatient = async (patientId: string) => {
    console.log('Attempting to delete patient with ID:', patientId);
    setIsLoading(true);

    try {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        console.log('No token found');
        Alert.alert('Error', 'Authentication token not found');
        setIsLoading(false);
        return;
      }

      const res = await fetch(`${API_BASE_URL}/api/patients/${patientId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      // Try to parse the response as JSON
      let data;
      try {
        data = await res.json();
      } catch (e) {
        // If the response is not JSON, just continue
        console.log('Response is not JSON:', e);
      }

      if (res.status === 200 || res.status === 204) {
        console.log('Patient deleted successfully');

        // Update the patient list by removing the deleted patient
        setPatientData(prev => prev.filter(patient => patient.id !== patientId));

        // Show success message
        Alert.alert('Success', 'Patient deleted successfully');
      } else {
        const errorMessage = data?.error || data?.message || 'Failed to delete patient';
        console.error('Failed to delete patient:', errorMessage);
        Alert.alert('Error', errorMessage);
      }
    } catch (error) {
      console.error('Error deleting patient:', error);
      Alert.alert('Error', 'An error occurred while deleting the patient');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={[
        styles.header,
        screenWidth < SCREEN_BREAKPOINT_TABLET && styles.headerSmallScreen
      ]}>
        <View style={styles.headerLeftSection}>
          <Text style={styles.title}>Patients List</Text>
        </View>
      </View>

      <Searchbar
        placeholder="Search ...."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
        iconColor={theme.colors.primary}
      />

      <ScrollView style={styles.patientList}>
        {isLoading && filteredPatients.length === 0 ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Loading patients...</Text>
          </View>
        ) : filteredPatients.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No patients found</Text>
          </View>
        ) : (
          <View style={screenWidth >= SCREEN_BREAKPOINT_TABLET ? styles.patientGridContainer : {}}>
            {filteredPatients.map((patient) => (
            <Card
              key={patient.id || `patient-${patient.hospitalNo}-${patient.email}`}
              style={[
                styles.patientCard,
                screenWidth >= SCREEN_BREAKPOINT_TABLET && styles.patientCardGrid
              ]}
            >
              <Card.Content>
                <View style={[
                  styles.patientHeader,
                  screenWidth < 500 && styles.patientHeaderSmallScreen
                ]}>
                  <View style={screenWidth < 500 ? { flex: 1 } : {}}>
                    <Text style={styles.patientName}>{`${patient.firstName} ${patient.lastName}`}</Text>
                    <Text style={styles.patientEmail}>{patient.email}</Text>
                  </View>
                  <View style={styles.actionButtons}>
                    <Button
                      mode="outlined"
                      onPress={() => handleEditPatient(patient)}
                      style={styles.actionButton}
                      textColor={theme.colors.primary}
                      labelStyle={screenWidth < 400 ? { fontSize: 12 } : {}}
                    >
                      Edit
                    </Button>
                    <Button
                      mode="outlined"
                      textColor={theme.colors.error}
                      onPress={() => {
                        if (!patient) {
                          console.error('Cannot delete patient: Patient is undefined');
                          Alert.alert('Error', 'Cannot delete patient: Invalid patient data');
                          return;
                        }

                        // Use existing ID or generate a fallback ID
                        const patientId = patient.id || patient._id ||
                          `${patient.hospitalNo || ''}-${patient.email || ''}-${Date.now()}`;

                        if (patientId) {
                          console.log('Deleting patient with ID:', patientId);
                          handleDeletePatient(patientId);
                        } else {
                          console.error('Cannot delete patient: Unable to determine ID', patient);
                          Alert.alert('Error', 'Cannot delete patient: Unable to determine ID');
                        }
                      }}
                      style={styles.actionButton}
                      labelStyle={screenWidth < 400 ? { fontSize: 12 } : {}}
                    >
                      Delete
                    </Button>
                  </View>
                </View>
                <Divider style={styles.divider} />
                <View style={styles.patientDetailsContainer}>
                  <View style={[
                    styles.patientDetails,
                    screenWidth < 500 && styles.patientDetailsSmallScreen
                  ]}>
                    <Text style={[
                      styles.detailLabel,
                      screenWidth < 500 && styles.detailLabelSmallScreen
                    ]}>Hospital No:</Text>
                    <Text style={styles.detailValue}>{patient.hospitalNo}</Text>
                  </View>
                  <View style={[
                    styles.patientDetails,
                    screenWidth < 500 && styles.patientDetailsSmallScreen
                  ]}>
                    <Text style={[
                      styles.detailLabel,
                      screenWidth < 500 && styles.detailLabelSmallScreen
                    ]}>Age:</Text>
                    <Text style={styles.detailValue}>{patient.age} years</Text>
                  </View>
                  <View style={[
                    styles.patientDetails,
                    screenWidth < 500 && styles.patientDetailsSmallScreen
                  ]}>
                    <Text style={[
                      styles.detailLabel,
                      screenWidth < 500 && styles.detailLabelSmallScreen
                    ]}>Sex:</Text>
                    <Text style={styles.detailValue}>{patient.sex || 'Not specified'}</Text>
                  </View>
                  <View style={[
                    styles.patientDetails,
                    screenWidth < 500 && styles.patientDetailsSmallScreen
                  ]}>
                    <Text style={[
                      styles.detailLabel,
                      screenWidth < 500 && styles.detailLabelSmallScreen
                    ]}>Contact No:</Text>
                    <Text style={styles.detailValue}>{patient.contactNumber || 'Not specified'}</Text>
                  </View>
                </View>
              </Card.Content>
            </Card>
            ))}
          </View>
        )}
      </ScrollView>

      {/* Delete Confirmation Dialog */}
      <Portal>
        <Dialog
          visible={deleteDialogVisible}
          onDismiss={hideDeleteDialog}
          style={[styles.deleteDialogContainer, { width: screenWidth > 500 ? 400 : '85%', alignSelf: 'center' }]}
        >
          <View style={styles.iconContainer}>
            <Avatar.Icon
              size={screenWidth < SCREEN_BREAKPOINT_TABLET ? 48 : 56}
              icon="alert"
              color="white"
              style={{ backgroundColor: paperTheme.colors.error }}
            />
          </View>
          <Dialog.Title style={[
            styles.dialogTitle,
            screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 18, marginBottom: theme.spacing.xs }
          ]}>
            Confirm Deletion
          </Dialog.Title>
          <Dialog.Content style={styles.dialogContent}>
            <Paragraph style={[
              styles.dialogMessage,
              screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 14, marginBottom: theme.spacing.sm }
            ]}>
              Are you sure you want to delete this patient? This action cannot be undone.
            </Paragraph>
          </Dialog.Content>
          <Dialog.Actions style={[
            styles.dialogActions,
            screenWidth < SCREEN_BREAKPOINT_TABLET && {
              paddingHorizontal: theme.spacing.xs,
              paddingBottom: theme.spacing.xs,
              justifyContent: 'space-between'
            }
          ]}>
            <Button
              onPress={hideDeleteDialog}
              style={[
                styles.cancelButton,
                screenWidth < SCREEN_BREAKPOINT_TABLET && {
                  paddingHorizontal: theme.spacing.xs,
                  minWidth: 60
                }
              ]}
              labelStyle={[
                { color: paperTheme.colors.onSurface },
                screenWidth < SCREEN_BREAKPOINT_TABLET && { fontSize: 12 }
              ]}
              mode="outlined"
            >
              Cancel
            </Button>
            <Button
              onPress={handleDeleteConfirm}
              mode="contained"
              style={[
                styles.deleteButton,
                screenWidth < SCREEN_BREAKPOINT_TABLET && {
                  paddingHorizontal: theme.spacing.xs,
                  minWidth: 60
                }
              ]}
              labelStyle={screenWidth < SCREEN_BREAKPOINT_TABLET ? { fontSize: 12, color: 'white' } : { color: 'white' }}
              loading={isLoading}
              disabled={isLoading}
            >
              Delete
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Edit Patient Modal */}
      <Portal>
        <Modal
          visible={showEditModal}
          onDismiss={() => {
            console.log('Closing edit modal and cleaning up state');
            setShowEditModal(false);
            setSelectedPatient(null);
            setFormData({
              firstName: '',
              lastName: '',
              email: '',
              age: '',
              sex: '',
              contactNumber: '',
              hospitalNo: ''
            });
            setFormError('');
          }}
          contentContainerStyle={[
            styles.modalContainer,
            { width: screenWidth >= SCREEN_BREAKPOINT_TABLET ? '60%' : '85%' }
          ]}
        >
          <Text style={styles.modalTitle}>Edit Patient</Text>
          {formError ? <Text style={styles.error}>{formError}</Text> : null}

          <TextInput
            label="First Name"
            value={formData.firstName}
            onChangeText={(text) => setFormData(prev => ({ ...prev, firstName: text }))}
            style={styles.input}
            mode="outlined"
          />
          <TextInput
            label="Last Name"
            value={formData.lastName}
            onChangeText={(text) => setFormData(prev => ({ ...prev, lastName: text }))}
            style={styles.input}
            mode="outlined"
          />
          <TextInput
            label="Email"
            value={formData.email}
            onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
            style={styles.input}
            mode="outlined"
          />
          <TextInput
            label="Age"
            value={formData.age}
            onChangeText={(text) => setFormData(prev => ({ ...prev, age: text }))}
            keyboardType="numeric"
            style={styles.input}
            mode="outlined"
          />
          <TextInput
            label="Sex"
            value={formData.sex}
            onChangeText={(text) => setFormData(prev => ({ ...prev, sex: text }))}
            style={styles.input}
            mode="outlined"
          />
          <TextInput
            label="Contact Number"
            value={formData.contactNumber}
            onChangeText={(text) => setFormData(prev => ({ ...prev, contactNumber: text }))}
            style={styles.input}
            mode="outlined"
          />

          <View style={styles.modalButtons}>
            <Button
              mode="outlined"
              onPress={() => {
                console.log('Cancel button clicked, cleaning up state');
                setShowEditModal(false);
                setSelectedPatient(null);
                setFormData({
                  firstName: '',
                  lastName: '',
                  email: '',
                  age: '',
                  sex: '',
                  contactNumber: '',
                  hospitalNo: ''
                });
                setFormError('');
              }}
              style={styles.modalButton}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleUpdatePatient}
              style={styles.modalButton}
              buttonColor={theme.colors.primary}
              loading={isLoading}
            >
              Save
            </Button>
          </View>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...globalStyles.container,
    paddingHorizontal: Platform.OS === 'web' ? theme.spacing.lg : theme.spacing.md,
  },
  // Header styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    flexWrap: 'nowrap',
  },
  headerSmallScreen: {
    flexDirection: 'column',
    alignItems: 'stretch',
  },
  headerLeftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  // Search bar styles
  searchBar: {
    marginBottom: theme.spacing.md,
    backgroundColor: theme.colors.surface,
  },
  // Patient list styles
  patientList: {
    flex: 1,
  },
  patientGridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  patientCard: {
    ...globalStyles.card,
    marginVertical: theme.spacing.sm,
  },
  patientCardGrid: {
    width: '48%',
    marginHorizontal: '1%',
  },
  patientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  patientHeaderSmallScreen: {
    flexDirection: 'column',
  },
  patientName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  patientEmail: {
    fontSize: 14,
    color: theme.colors.placeholder,
    marginBottom: theme.spacing.xs,
  },
  divider: {
    marginVertical: theme.spacing.sm,
  },
  patientDetailsContainer: {
    flexDirection: 'column',
  },
  patientDetails: {
    flexDirection: 'row',
    marginVertical: theme.spacing.xs,
  },
  patientDetailsSmallScreen: {
    flexDirection: 'column',
    marginBottom: theme.spacing.sm,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.placeholder,
    width: 120,
  },
  detailLabelSmallScreen: {
    width: '100%',
    fontWeight: 'bold',
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 14,
    color: theme.colors.text,
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: theme.spacing.sm,
  },
  actionButton: {
    marginLeft: theme.spacing.sm,
  },
  // Delete confirmation dialog styles
  deleteDialogContainer: {
    borderRadius: theme.roundness * 2,
    backgroundColor: theme.colors.surface,
    ...globalStyles.shadow,
  },
  iconContainer: {
    alignItems: 'center',
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
  },
  dialogTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.error,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  dialogContent: {
    paddingHorizontal: theme.spacing.sm,
    paddingBottom: theme.spacing.sm,
  },
  dialogMessage: {
    fontSize: 16,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  dialogActions: {
    marginTop: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
    paddingBottom: theme.spacing.sm,
    justifyContent: 'space-between',
  },
  cancelButton: {
    marginRight: theme.spacing.sm,
  },
  deleteButton: {
    backgroundColor: theme.colors.error,
  },
  // Modal styles
  modalContainer: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    margin: theme.spacing.md,
    borderRadius: theme.roundness,
    alignSelf: 'center',
    maxWidth: 800,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.spacing.md,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: theme.spacing.md,
  },
  modalButton: {
    marginLeft: theme.spacing.sm,
  },
  error: {
    color: theme.colors.error,
    marginBottom: theme.spacing.sm,
  },
  input: {
    marginBottom: theme.spacing.sm,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
    minHeight: 200,
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: 16,
    color: theme.colors.primary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
    minHeight: 200,
  },
  emptyText: {
    fontSize: 16,
    color: theme.colors.placeholder,
  },
});
