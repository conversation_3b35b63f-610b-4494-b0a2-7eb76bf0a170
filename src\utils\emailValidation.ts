/**
 * Email validation utilities for the Drug Counselling App
 * Provides comprehensive email validation and formatting functions
 */

/**
 * Basic email validation using regex
 * @param email - Email address to validate
 * @returns boolean indicating if email is valid
 */
export const isValidEmail = (email: string): boolean => {
  if (!email || typeof email !== 'string') {
    return false;
  }

  // Trim whitespace
  const trimmedEmail = email.trim();
  
  // Check if empty after trimming
  if (!trimmedEmail) {
    return false;
  }

  // Basic email regex pattern
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  // Additional checks for common issues
  const hasValidFormat = emailRegex.test(trimmedEmail);
  const hasValidLength = trimmedEmail.length <= 254; // RFC 5321 limit
  const hasValidLocalPart = trimmedEmail.split('@')[0]?.length <= 64; // RFC 5321 limit
  
  return hasValidFormat && hasValidLength && hasValidLocalPart;
};

/**
 * More comprehensive email validation with detailed error messages
 * @param email - Email address to validate
 * @returns object with isValid boolean and error message if invalid
 */
export const validateEmailWithMessage = (email: string): { isValid: boolean; error?: string } => {
  if (!email || typeof email !== 'string') {
    return { isValid: false, error: 'Email is required' };
  }

  const trimmedEmail = email.trim();
  
  if (!trimmedEmail) {
    return { isValid: false, error: 'Email is required' };
  }

  if (trimmedEmail.length > 254) {
    return { isValid: false, error: 'Email address is too long' };
  }

  const parts = trimmedEmail.split('@');
  if (parts.length !== 2) {
    return { isValid: false, error: 'Email must contain exactly one @ symbol' };
  }

  const [localPart, domainPart] = parts;

  if (!localPart || localPart.length === 0) {
    return { isValid: false, error: 'Email must have a username before @' };
  }

  if (localPart.length > 64) {
    return { isValid: false, error: 'Username part of email is too long' };
  }

  if (!domainPart || domainPart.length === 0) {
    return { isValid: false, error: 'Email must have a domain after @' };
  }

  if (!domainPart.includes('.')) {
    return { isValid: false, error: 'Email domain must contain at least one dot' };
  }

  // Check for consecutive dots
  if (domainPart.includes('..')) {
    return { isValid: false, error: 'Email domain cannot contain consecutive dots' };
  }

  // Check for valid characters
  const validEmailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!validEmailRegex.test(trimmedEmail)) {
    return { isValid: false, error: 'Email contains invalid characters' };
  }

  return { isValid: true };
};

/**
 * Normalize email address (trim and lowercase)
 * @param email - Email address to normalize
 * @returns normalized email address
 */
export const normalizeEmail = (email: string): string => {
  if (!email || typeof email !== 'string') {
    return '';
  }
  
  return email.trim().toLowerCase();
};

/**
 * Check if email domain is from a common provider
 * @param email - Email address to check
 * @returns boolean indicating if it's from a common provider
 */
export const isCommonEmailProvider = (email: string): boolean => {
  if (!isValidEmail(email)) {
    return false;
  }

  const domain = email.split('@')[1]?.toLowerCase();
  const commonProviders = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'icloud.com',
    'aol.com',
    'protonmail.com',
    'mail.com'
  ];

  return commonProviders.includes(domain);
};

/**
 * Extract domain from email address
 * @param email - Email address
 * @returns domain part of email or empty string if invalid
 */
export const getEmailDomain = (email: string): string => {
  if (!isValidEmail(email)) {
    return '';
  }
  
  return email.split('@')[1]?.toLowerCase() || '';
};

/**
 * Check if email appears to be a temporary/disposable email
 * @param email - Email address to check
 * @returns boolean indicating if it might be temporary
 */
export const isPotentiallyTemporaryEmail = (email: string): boolean => {
  if (!isValidEmail(email)) {
    return false;
  }

  const domain = getEmailDomain(email);
  const temporaryDomains = [
    '10minutemail.com',
    'tempmail.org',
    'guerrillamail.com',
    'mailinator.com',
    'throwaway.email',
    'temp-mail.org'
  ];

  return temporaryDomains.includes(domain);
};

/**
 * Format email for display (truncate if too long)
 * @param email - Email address to format
 * @param maxLength - Maximum length for display (default: 30)
 * @returns formatted email string
 */
export const formatEmailForDisplay = (email: string, maxLength: number = 30): string => {
  if (!email || typeof email !== 'string') {
    return '';
  }

  const trimmedEmail = email.trim();
  
  if (trimmedEmail.length <= maxLength) {
    return trimmedEmail;
  }

  // Try to keep the domain visible
  const atIndex = trimmedEmail.lastIndexOf('@');
  if (atIndex > 0 && trimmedEmail.length - atIndex < maxLength - 3) {
    const localPart = trimmedEmail.substring(0, atIndex);
    const domainPart = trimmedEmail.substring(atIndex);
    const availableLength = maxLength - domainPart.length - 3; // 3 for "..."
    
    if (availableLength > 0) {
      return localPart.substring(0, availableLength) + '...' + domainPart;
    }
  }

  // Fallback: just truncate with ellipsis
  return trimmedEmail.substring(0, maxLength - 3) + '...';
};

/**
 * Validate email for password reset specifically
 * @param email - Email address to validate
 * @returns validation result with specific messaging for password reset
 */
export const validateEmailForPasswordReset = (email: string): { isValid: boolean; error?: string } => {
  const basicValidation = validateEmailWithMessage(email);
  
  if (!basicValidation.isValid) {
    return basicValidation;
  }

  // Additional checks for password reset
  if (isPotentiallyTemporaryEmail(email)) {
    return { 
      isValid: false, 
      error: 'Password reset is not available for temporary email addresses' 
    };
  }

  return { isValid: true };
};
